/**
 * 组合方案测试
 * 方案B(HTML预处理) + 方案A(XML补丁)
 * 先用HTML预处理尝试处理着重号，再用XML补丁修复处理不好的部分
 */
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function testCombinedApproach() {
  console.log('\n=== 组合方案测试：方案B + 方案A ===');
  
  const wordExportService = new WordExportService();
  
  const testHtml = `
    <html>
      <body>
        <h1>组合方案测试</h1>
        
        <h2>一、着重号测试（组合处理）</h2>
        <p><strong>1. 下面对有新疆房的语言的理解，错误的一项是（　　）</strong></p>
        <p>A. A. 边疆（<span data-emphasis-mark="dot">重返国界的领土</span>）</p>
        <p>B. B. 绚丽多彩（<span data-emphasis-mark="dot">各种各样的色彩</span>）</p>
        <p>C. C. 坪坝（<span data-emphasis-mark="dot">高高低低的坝地</span>）</p>
        <p>D. D. 高高低低工厂对有新疆房的可了的</p>
        
        <h2>二、表格测试（html-to-docx完整处理）</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th style="background-color: #f0f0f0; padding: 8px;">题型</th>
            <th style="background-color: #f0f0f0; padding: 8px;">内容</th>
            <th style="background-color: #f0f0f0; padding: 8px;">着重号测试</th>
          </tr>
          <tr>
            <td style="padding: 8px;">选择题</td>
            <td style="padding: 8px;">普通文字内容</td>
            <td style="padding: 8px;"><span data-emphasis-mark="dot">着重号文字</span></td>
          </tr>
          <tr>
            <td style="padding: 8px;">填空题</td>
            <td style="padding: 8px;">更多测试内容</td>
            <td style="padding: 8px;">无着重号</td>
          </tr>
          <tr>
            <td style="padding: 8px;">判断题</td>
            <td style="padding: 8px;"><span data-emphasis-mark="dot">表格内着重号</span></td>
            <td style="padding: 8px;">混合内容</td>
          </tr>
        </table>
        
        <h2>三、复杂格式测试（html-to-docx完整处理）</h2>
        <p style="text-align: center; color: blue; font-size: 16px;">
          居中蓝色段落，包含<span data-emphasis-mark="dot">着重号文字</span>，测试格式保持。
        </p>
        <p style="text-align: right; font-weight: bold;">
          右对齐粗体段落，包含<span data-emphasis-mark="dot">更多着重号</span>。
        </p>
        
        <h2>四、列表测试（html-to-docx完整处理）</h2>
        <ul>
          <li>列表项1：<span data-emphasis-mark="dot">着重号内容</span></li>
          <li>列表项2：普通内容</li>
          <li>列表项3：混合<span data-emphasis-mark="dot">着重号</span>和普通文字</li>
        </ul>
        
        <ol>
          <li>有序列表1：<span data-emphasis-mark="dot">数字着重号</span></li>
          <li>有序列表2：正常内容</li>
        </ol>
        
        <h2>五、嵌套结构测试</h2>
        <div style="border: 2px solid red; padding: 10px; margin: 10px;">
          <h3>边框容器内的内容</h3>
          <p>包含<span data-emphasis-mark="dot">着重号</span>的段落内容。</p>
          <table style="width: 100%;" border="1">
            <tr>
              <td>容器内表格</td>
              <td><span data-emphasis-mark="dot">容器内着重号</span></td>
            </tr>
          </table>
        </div>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'combined-approach-test.docx');
    
    // 使用组合方案
    await wordExportService.exportHtmlToWordFile(testHtml, outputPath, {
      title: '组合方案测试',
      author: '测试系统',
      xmlPatchOptions: {
        patchEmphasisMarks: true, // 启用XML补丁修复着重号
        patchSpecialFonts: false, // 暂不处理特殊字体
      }
    });
    
    console.log('✅ 组合方案测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('\n🔧 组合处理流程：');
    console.log('1. 方案B：HTML预处理尝试处理着重号');
    console.log('2. html-to-docx：完整处理表格、列表、段落等所有内容');
    console.log('3. 方案A：XML补丁最小范围修复着重号');
    console.log('\n📋 预期效果：');
    console.log('- 表格：html-to-docx完整处理，格式完美');
    console.log('- 段落：html-to-docx完整处理，对齐和颜色保持');
    console.log('- 列表：html-to-docx完整处理，结构完整');
    console.log('- 着重号：方案B尝试 + 方案A补丁修复');
    return true;
  } catch (error) {
    console.log('❌ 组合方案测试失败:', error.message);
    return false;
  }
}

async function testComplexDocumentCombined() {
  console.log('\n=== 复杂文档组合方案测试 ===');
  
  const wordExportService = new WordExportService();
  
  try {
    // 读取原始的复杂HTML文件
    const fs = require('fs');
    const htmlFilePath = path.join(process.cwd(), 'testFiles', 'input-html-sample.html');
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf-8');
    console.log(`已读取HTML文件: ${htmlFilePath}`);

    const outputPath = path.join(process.cwd(), 'testFiles', 'complex-combined.docx');
    
    // 使用组合方案处理复杂文档
    await wordExportService.exportHtmlToWordFile(htmlContent, outputPath, {
      title: '复杂文档组合方案',
      author: '测试系统',
      xmlPatchOptions: {
        patchEmphasisMarks: true, // 修复着重号
        patchSpecialFonts: false, // 预留扩展
      }
    });
    
    console.log('✅ 复杂文档组合方案测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：');
    console.log('- 所有表格、段落、列表格式完整保持（html-to-docx）');
    console.log('- 着重号通过组合方案正确显示（方案B + 方案A）');
    console.log('- 整体文档结构和样式完美');
    return true;
  } catch (error) {
    console.log('❌ 复杂文档组合方案测试失败:', error.message);
    return false;
  }
}

async function testNoEmphasisCombined() {
  console.log('\n=== 无着重号组合方案测试 ===');
  
  const wordExportService = new WordExportService();
  
  const testHtml = `
    <html>
      <body>
        <h1>无着重号文档测试</h1>
        
        <h2>一、普通内容</h2>
        <p>这是普通段落，没有着重号。</p>
        <p><strong>这是粗体段落</strong>，也没有着重号。</p>
        <p style="color: red;">这是红色段落。</p>
        
        <h2>二、表格测试</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th style="background-color: #f0f0f0;">标题1</th>
            <th style="background-color: #f0f0f0;">标题2</th>
          </tr>
          <tr>
            <td>内容1</td>
            <td>内容2</td>
          </tr>
        </table>
        
        <h2>三、列表测试</h2>
        <ul>
          <li>列表项1</li>
          <li>列表项2</li>
        </ul>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'no-emphasis-combined.docx');
    
    // 无着重号文档，XML补丁应该跳过
    await wordExportService.exportHtmlToWordFile(testHtml, outputPath, {
      title: '无着重号组合方案测试',
      author: '测试系统',
      xmlPatchOptions: {
        patchEmphasisMarks: true, // 启用但应该跳过
      }
    });
    
    console.log('✅ 无着重号组合方案测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：');
    console.log('- XML补丁检测到无着重号，直接返回html-to-docx结果');
    console.log('- 所有格式完整保持');
    console.log('- 处理效率最优');
    return true;
  } catch (error) {
    console.log('❌ 无着重号组合方案测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('开始组合方案测试...');
  console.log('组合方案：方案B(HTML预处理) + 方案A(XML补丁)');
  
  const tests = [
    testCombinedApproach,
    testComplexDocumentCombined,
    testNoEmphasisCombined,
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    const result = await test();
    if (result) {
      passedTests++;
    }
  }
  
  console.log('\n=== 组合方案测试总结 ===');
  console.log(`总测试数: ${tests.length}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${tests.length - passedTests}`);
  
  if (passedTests === tests.length) {
    console.log('🎉 组合方案测试通过！');
    console.log('\n🏗️ 组合方案优势：');
    console.log('1. 方案B：HTML预处理尝试处理着重号，简单直接');
    console.log('2. html-to-docx：完整处理所有内容，保持所有功能');
    console.log('3. 方案A：XML补丁最小范围修复，不影响其他内容');
    console.log('4. 渐进式处理：先简单后复杂，逐步完善');
    console.log('5. 最佳平衡：功能完整 + 着重号修复');
    console.log('\n这是您要求的组合方案！');
  } else {
    console.log('⚠️  部分组合方案测试失败，请检查相关功能。');
  }
  
  console.log('\n📁 生成的测试文件：');
  console.log('- combined-approach-test.docx - 组合方案完整测试');
  console.log('- complex-combined.docx - 复杂文档组合方案');
  console.log('- no-emphasis-combined.docx - 无着重号组合方案');
  console.log('\n请手动检查Word文档，验证着重号效果！');
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

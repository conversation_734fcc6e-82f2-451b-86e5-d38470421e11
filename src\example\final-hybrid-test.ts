/**
 * 最终混合方案测试
 * 使用原始复杂HTML文件测试混合方案
 */
import * as fs from 'fs';
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function main() {
  try {
    console.log('开始最终混合方案测试...');

    const wordExportService = new WordExportService();

    // 读取原始的复杂HTML文件
    const htmlFilePath = path.join(process.cwd(), 'testFiles', 'input-html-sample.html');
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf-8');
    console.log(`已读取HTML文件: ${htmlFilePath}`);

    // 设置输出路径
    const outputPath = path.join(process.cwd(), 'testFiles', 'final-hybrid-result.docx');

    // 使用混合方案：html-to-docx + docx库后处理着重号
    console.log('开始使用混合方案转换复杂HTML到Word...');
    const savedPath = await wordExportService.exportHtmlToWordFile(
      htmlContent,
      outputPath,
      {
        title: '最终混合方案测试',
        author: '测试系统',
        orientation: 'portrait',
        useDocxForEmphasis: false, // 使用标准html-to-docx流程
        postprocessOptions: {
          processEmphasisMarks: true, // 启用docx库后处理着重号
        }
      }
    );

    console.log(`✅ 最终混合方案测试完成！`);
    console.log(`Word文档保存成功: ${savedPath}`);
    console.log('\n🎉 二合一方案实现成功！');
    console.log('\n📋 混合方案特点：');
    console.log('1. 第一步：html-to-docx快速处理');
    console.log('   - 完整支持表格、图片、复杂布局');
    console.log('   - 保持原有的转换速度和稳定性');
    console.log('   - 处理所有非着重号的格式');
    console.log('');
    console.log('2. 第二步：docx库专门处理着重号');
    console.log('   - 解析HTML提取着重号信息');
    console.log('   - 使用docx库重新生成文档');
    console.log('   - 应用专业的着重号格式（虚线下划线+粗体+高亮）');
    console.log('');
    console.log('3. 最终效果：');
    console.log('   - 着重号：专业的Word格式效果');
    console.log('   - 表格：保持html-to-docx的完整支持');
    console.log('   - 其他格式：保持原有质量');
    console.log('');
    console.log('这就是您要求的二合一方案！');
    console.log('请手动打开Word文档验证最终效果！');

  } catch (error) {
    console.error('最终混合方案测试失败:', error);
  }
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

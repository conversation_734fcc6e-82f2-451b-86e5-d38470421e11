/**
 * 简单表格测试
 * 验证表格边框的简化处理效果
 */
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function main() {
  try {
    console.log('开始简单表格测试...');

    const wordExportService = new WordExportService();

    // 简单的表格HTML
    const tableHtml = `
      <html>
        <body>
          <h1>简单表格测试</h1>
          <p>这是一个简单的表格，测试边框效果：</p>
          
          <table style="width: 100%;">
            <tr>
              <th>标题1</th>
              <th>标题2</th>
              <th>标题3</th>
            </tr>
            <tr>
              <td>内容1</td>
              <td>内容2</td>
              <td>内容3</td>
            </tr>
            <tr>
              <td>数据A</td>
              <td>数据B</td>
              <td>数据C</td>
            </tr>
          </table>
          
          <p>预期效果：简单的单线边框，不复杂。</p>
        </body>
      </html>
    `;

    const outputPath = path.join(process.cwd(), 'testFiles', 'simple-table-border-test.docx');

    await wordExportService.exportHtmlToWordFile(tableHtml, outputPath, {
      title: '简单表格边框测试',
      author: '测试系统'
    });

    console.log('✅ 简单表格测试完成');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('请打开文档查看表格边框效果，应该是简单的单线边框。');

  } catch (error) {
    console.error('简单表格测试失败:', error);
  }
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

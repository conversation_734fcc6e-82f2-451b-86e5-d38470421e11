/**
 * Word文档XML补丁处理器
 * 方案A：对html-to-docx生成的Word文档进行最小范围的XML修改
 * 只处理html-to-docx无法正确处理的特殊样式，不影响其他已处理好的内容
 */
import * as JSZip from 'jszip';
import { DOMParser, XMLSerializer } from 'xmldom';

export interface XmlPatchOptions {
  /** 是否修复着重号 */
  patchEmphasisMarks?: boolean;
  /** 是否修复特殊字体 */
  patchSpecialFonts?: boolean;
  /** 原始HTML内容，用于定位需要修复的内容 */
  originalHtmlContent?: string;
}

export interface EmphasisTarget {
  text: string;
  shouldHaveEmphasis: boolean;
}

/**
 * Word文档XML补丁处理器
 * 对html-to-docx的输出进行最小范围的修正
 */
export class WordXmlPatcher {
  private docxBuffer: Buffer;
  private originalHtml: string;

  constructor(docxBuffer: Buffer, originalHtml: string = '') {
    this.docxBuffer = docxBuffer;
    this.originalHtml = originalHtml;
  }

  /**
   * 应用补丁
   */
  async applyPatches(options: XmlPatchOptions = {}): Promise<Buffer> {
    const {
      patchEmphasisMarks = true,
      patchSpecialFonts = false,
      originalHtmlContent,
    } = options;

    console.log('开始Word文档XML补丁处理...');

    // 检查是否需要补丁处理
    const needsPatching = this.needsPatching(options);
    
    if (!needsPatching) {
      console.log('无需XML补丁处理，返回原始文档');
      return this.docxBuffer;
    }

    try {
      // 解析Word文档
      const zip = new JSZip();
      const docxZip = await zip.loadAsync(this.docxBuffer);
      
      // 读取document.xml
      const documentXml = await docxZip.file('word/document.xml')?.async('text');
      if (!documentXml) {
        throw new Error('无法读取Word文档内容');
      }

      console.log('成功读取Word文档XML');

      // 解析XML
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(documentXml, 'text/xml');

      let modified = false;

      // 应用着重号补丁
      if (patchEmphasisMarks) {
        const emphasisPatched = await this.patchEmphasisMarks(xmlDoc, originalHtmlContent || this.originalHtml);
        if (emphasisPatched) {
          modified = true;
          console.log('着重号XML补丁应用成功');
        }
      }

      // 应用其他补丁（预留）
      if (patchSpecialFonts) {
        // TODO: 实现特殊字体补丁
        console.log('特殊字体补丁处理（预留）');
      }

      if (modified) {
        // 序列化修改后的XML
        const serializer = new XMLSerializer();
        const modifiedXml = serializer.serializeToString(xmlDoc);

        // 更新ZIP中的document.xml
        docxZip.file('word/document.xml', modifiedXml);

        // 生成新的Word文档
        const patchedBuffer = await docxZip.generateAsync({ type: 'nodebuffer' });
        
        console.log('Word文档XML补丁处理完成');
        return patchedBuffer;
      } else {
        console.log('无需修改，返回原始文档');
        return this.docxBuffer;
      }

    } catch (error) {
      console.error('XML补丁处理失败，回退到原始文档:', error);
      return this.docxBuffer;
    }
  }

  /**
   * 检查是否需要补丁处理
   */
  private needsPatching(options: XmlPatchOptions): boolean {
    if (options.patchEmphasisMarks && this.hasEmphasisMarks()) {
      console.log('检测到着重号，需要XML补丁处理');
      return true;
    }

    if (options.patchSpecialFonts && this.hasSpecialFonts()) {
      console.log('检测到特殊字体，需要XML补丁处理');
      return true;
    }

    return false;
  }

  /**
   * 检查是否有着重号（特殊标记）
   */
  private hasEmphasisMarks(): boolean {
    return this.originalHtml.includes('[EMPHASIS]') &&
           this.originalHtml.includes('[/EMPHASIS]');
  }

  /**
   * 检查是否有特殊字体（预留）
   */
  private hasSpecialFonts(): boolean {
    // TODO: 实现特殊字体检测
    return false;
  }

  /**
   * 应用着重号补丁
   * 在Word XML中找到对应文本，添加着重号样式
   */
  private async patchEmphasisMarks(xmlDoc: Document, htmlContent: string): Promise<boolean> {
    try {
      // 从HTML中提取需要着重号的文本
      const emphasisTargets = this.extractEmphasisTargets(htmlContent);
      
      if (emphasisTargets.length === 0) {
        console.log('未找到需要处理的着重号');
        return false;
      }

      console.log(`找到 ${emphasisTargets.length} 个需要着重号的文本`);

      // 在Word XML中查找并修改对应的文本节点
      let patchCount = 0;
      
      for (const target of emphasisTargets) {
        if (this.patchTextNodeEmphasis(xmlDoc, target.text)) {
          patchCount++;
        }
      }

      console.log(`成功应用 ${patchCount} 个着重号补丁`);
      return patchCount > 0;

    } catch (error) {
      console.error('着重号补丁处理失败:', error);
      return false;
    }
  }

  /**
   * 从HTML中提取需要着重号的文本
   */
  private extractEmphasisTargets(htmlContent: string): EmphasisTarget[] {
    const targets: EmphasisTarget[] = [];

    try {
      // 使用正则表达式提取特殊标记中的文本
      const emphasisRegex = /\[EMPHASIS\](.*?)\[\/EMPHASIS\]/gi;
      let match;

      while ((match = emphasisRegex.exec(htmlContent)) !== null) {
        const text = match[1].replace(/<[^>]*>/g, '').trim(); // 移除内部HTML标签
        if (text) {
          targets.push({
            text,
            shouldHaveEmphasis: true
          });
        }
      }

      console.log(`从HTML中提取了 ${targets.length} 个着重号目标（特殊标记）`);
    } catch (error) {
      console.error('提取着重号目标失败:', error);
    }

    return targets;
  }

  /**
   * 在Word XML中为指定文本添加着重号样式
   */
  private patchTextNodeEmphasis(xmlDoc: Document, targetText: string): boolean {
    try {
      // 查找所有文本节点
      const textNodes = xmlDoc.getElementsByTagName('w:t');
      
      for (let i = 0; i < textNodes.length; i++) {
        const textNode = textNodes[i];
        const textContent = textNode.textContent || '';
        
        if (textContent.includes(targetText)) {
          console.log(`找到目标文本: "${targetText}"`);
          
          // 找到包含此文本节点的运行(w:r)
          const runNode = this.findParentRun(textNode);
          if (runNode) {
            // 添加着重号样式到运行属性
            this.addEmphasisToRun(runNode);
            console.log(`为文本 "${targetText}" 添加着重号样式`);
            return true;
          }
        }
      }
      
      console.log(`未找到目标文本: "${targetText}"`);
      return false;
    } catch (error) {
      console.error(`为文本 "${targetText}" 添加着重号失败:`, error);
      return false;
    }
  }

  /**
   * 查找文本节点的父运行节点
   */
  private findParentRun(textNode: Node): Element | null {
    let parent = textNode.parentNode;
    while (parent) {
      if (parent.nodeName === 'w:r') {
        return parent as Element;
      }
      parent = parent.parentNode;
    }
    return null;
  }

  /**
   * 为运行节点添加着重号样式
   */
  private addEmphasisToRun(runNode: Element): void {
    const doc = runNode.ownerDocument;
    if (!doc) return;

    // 查找或创建运行属性节点
    let rPrNode = runNode.getElementsByTagName('w:rPr')[0];
    if (!rPrNode) {
      rPrNode = doc.createElement('w:rPr');
      runNode.insertBefore(rPrNode, runNode.firstChild);
    }

    // 只添加着重号，不改变文字的其他样式（如粗细、颜色等）
    if (!rPrNode.getElementsByTagName('w:em')[0]) {
      const emphasisNode = doc.createElement('w:em');
      emphasisNode.setAttribute('w:val', 'dot');
      rPrNode.appendChild(emphasisNode);
    }

    // 不添加粗体，不添加高亮背景，保持文字原有样式
  }
}

/**
 * 便捷函数：对Word文档应用XML补丁
 */
export async function patchWordDocument(
  docxBuffer: Buffer,
  originalHtml: string,
  options: XmlPatchOptions = {}
): Promise<Buffer> {
  const patcher = new WordXmlPatcher(docxBuffer, originalHtml);
  return await patcher.applyPatches(options);
}

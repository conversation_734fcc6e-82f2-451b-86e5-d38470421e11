/**
 * 使用docx库处理着重号的测试
 * 验证docx库是否能正确处理着重号
 */
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function testDocxEmphasisProcessing() {
  console.log('\n=== 测试使用docx库处理着重号 ===');
  
  const wordExportService = new WordExportService();
  
  const emphasisHtml = `
    <html>
      <body>
        <h1>docx库着重号测试</h1>
        <p>这里有<span style="text-emphasis: filled currentColor; text-emphasis-position: under right;" data-emphasis-mark="dot">着重号文字1</span>。</p>
        <p>还有<span data-emphasis-mark="dot" style="text-emphasis: filled currentColor;">着重号文字2</span>。</p>
        <p>以及<span data-emphasis-mark="dot">仅有属性的着重号</span>。</p>
        <p>普通文字没有着重号。</p>
        <p>预期效果：使用docx库的下划线+粗体+高亮来表示着重号</p>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'docx-emphasis-test.docx');
    
    // 使用docx库处理着重号
    await wordExportService.exportHtmlToWordFile(emphasisHtml, outputPath, {
      title: 'docx库着重号测试',
      author: '测试系统',
      useDocxForEmphasis: true, // 启用docx库处理着重号
      emphasisOptions: {
        emphasisStyle: 'dot',
        useUnderlineAsEmphasis: true,
        useBoldForEmphasis: true,
      }
    });
    
    console.log('✅ docx库着重号处理测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：着重号文字应该有下划线+粗体+黄色高亮');
    return true;
  } catch (error) {
    console.log('❌ docx库着重号处理测试失败:', error.message);
    return false;
  }
}

async function testStandardEmphasisProcessing() {
  console.log('\n=== 测试标准着重号处理（对比） ===');
  
  const wordExportService = new WordExportService();
  
  const emphasisHtml = `
    <html>
      <body>
        <h1>标准着重号测试</h1>
        <p>这里有<span style="text-emphasis: filled currentColor; text-emphasis-position: under right;" data-emphasis-mark="dot">着重号文字1</span>。</p>
        <p>还有<span data-emphasis-mark="dot" style="text-emphasis: filled currentColor;">着重号文字2</span>。</p>
        <p>以及<span data-emphasis-mark="dot">仅有属性的着重号</span>。</p>
        <p>普通文字没有着重号。</p>
        <p>预期效果：使用html-to-docx的粗体+背景色+下划线来表示着重号</p>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'standard-emphasis-test.docx');
    
    // 使用标准处理流程
    await wordExportService.exportHtmlToWordFile(emphasisHtml, outputPath, {
      title: '标准着重号测试',
      author: '测试系统',
      useDocxForEmphasis: false, // 使用标准处理流程
    });
    
    console.log('✅ 标准着重号处理测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：着重号文字应该有粗体+黄色背景+下划线');
    return true;
  } catch (error) {
    console.log('❌ 标准着重号处理测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('开始着重号处理对比测试...');
  
  const tests = [
    testDocxEmphasisProcessing,
    testStandardEmphasisProcessing,
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    const result = await test();
    if (result) {
      passedTests++;
    }
  }
  
  console.log('\n=== 着重号处理对比测试总结 ===');
  console.log(`总测试数: ${tests.length}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${tests.length - passedTests}`);
  
  if (passedTests === tests.length) {
    console.log('🎉 所有着重号处理测试通过！');
    console.log('\n📋 对比结果：');
    console.log('1. docx库方案：下划线+粗体+黄色高亮（更接近真正的着重号）');
    console.log('2. 标准方案：粗体+黄色背景+下划线（html-to-docx处理）');
    console.log('\n请手动打开两个Word文档对比效果！');
  } else {
    console.log('⚠️  部分着重号处理测试失败，请检查相关功能。');
  }
  
  console.log('\n📁 生成的测试文件：');
  console.log('- docx-emphasis-test.docx - 使用docx库处理的着重号');
  console.log('- standard-emphasis-test.docx - 使用标准流程处理的着重号');
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

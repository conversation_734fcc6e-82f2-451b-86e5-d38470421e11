/**
 * 最终测试脚本
 * 验证HTML转Word功能的完整性
 */
import * as fs from 'fs';
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function testBasicConversion() {
  console.log('\n=== 测试1: 基本HTML转换 ===');

  const wordExportService = new WordExportService();

  const simpleHtml = `
    <html>
      <body>
        <h1>测试文档</h1>
        <p>这是一个<strong>基本测试</strong>。</p>
        <p style="color: red;">红色文字测试。</p>
      </body>
    </html>
  `;

  try {
    const outputPath = path.join(
      process.cwd(),
      'testFiles',
      'test1-basic.docx'
    );
    await wordExportService.exportHtmlToWordFile(simpleHtml, outputPath, {
      title: '基本测试',
      author: '测试系统',
    });
    console.log('✅ 基本转换测试通过');
    return true;
  } catch (error) {
    console.log('❌ 基本转换测试失败:', error.message);
    return false;
  }
}

async function testEmphasisMarks() {
  console.log('\n=== 测试2: 着重号处理 ===');

  const wordExportService = new WordExportService();

  const emphasisHtml = `
    <html>
      <body>
        <h1>着重号测试</h1>
        <p>这里有<span style="text-emphasis: filled currentColor; text-emphasis-position: under right;" data-emphasis-mark="dot">着重号文字</span>。</p>
        <p>普通文字和<span data-emphasis-mark="dot" style="text-emphasis: filled currentColor;">另一个着重号</span>。</p>
      </body>
    </html>
  `;

  try {
    const outputPath = path.join(
      process.cwd(),
      'testFiles',
      'test2-emphasis.docx'
    );
    await wordExportService.exportHtmlToWordFile(emphasisHtml, outputPath, {
      title: '着重号测试',
      author: '测试系统',
    });
    console.log('✅ 着重号处理测试通过');
    return true;
  } catch (error) {
    console.log('❌ 着重号处理测试失败:', error.message);
    return false;
  }
}

async function testTableConversion() {
  console.log('\n=== 测试3: 表格转换 ===');

  const wordExportService = new WordExportService();

  const tableHtml = `
    <html>
      <body>
        <h1>表格测试</h1>
        <table style="width: 100%;">
          <tr>
            <th width="50%">标题1</th>
            <th width="50%">标题2</th>
          </tr>
          <tr>
            <td style="width: 50%;">内容1</td>
            <td style="width: 50%;">内容2</td>
          </tr>
        </table>
      </body>
    </html>
  `;

  try {
    const outputPath = path.join(
      process.cwd(),
      'testFiles',
      'test3-table.docx'
    );
    await wordExportService.exportHtmlToWordFile(tableHtml, outputPath, {
      title: '表格测试',
      author: '测试系统',
    });
    console.log('✅ 表格转换测试通过');
    return true;
  } catch (error) {
    console.log('❌ 表格转换测试失败:', error.message);
    return false;
  }
}

async function testComplexDocument() {
  console.log('\n=== 测试4: 复杂文档转换 ===');

  const wordExportService = new WordExportService();

  try {
    const htmlFilePath = path.join(
      process.cwd(),
      'testFiles',
      'input-html-sample.html'
    );
    const outputPath = path.join(
      process.cwd(),
      'testFiles',
      'test4-complex.docx'
    );

    const htmlContent = fs.readFileSync(htmlFilePath, 'utf-8');

    await wordExportService.exportHtmlToWordFile(htmlContent, outputPath, {
      title: '复杂文档测试',
      author: '测试系统',
      orientation: 'portrait',
    });
    console.log('✅ 复杂文档转换测试通过');
    return true;
  } catch (error) {
    console.log('❌ 复杂文档转换测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('开始HTML转Word功能完整性测试...\n');

  const tests = [
    testBasicConversion,
    testEmphasisMarks,
    testTableConversion,
    testComplexDocument,
  ];

  let passedTests = 0;

  for (const test of tests) {
    const result = await test();
    if (result) {
      passedTests++;
    }
  }

  console.log('\n=== 测试总结 ===');
  console.log(`总测试数: ${tests.length}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${tests.length - passedTests}`);

  if (passedTests === tests.length) {
    console.log('🎉 所有测试通过！HTML转Word功能实现成功！');
  } else {
    console.log('⚠️  部分测试失败，请检查相关功能。');
  }

  console.log(
    '\n生成的测试文件位于 testFiles/ 目录中，请手动打开验证转换效果。'
  );
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

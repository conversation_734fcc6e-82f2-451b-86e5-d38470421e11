/**
 * 简单的Word导出测试
 * 用于验证基本的HTML转Word功能
 */
import * as fs from 'fs';
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function main() {
  try {
    console.log('开始简单Word导出测试...');

    // 创建服务实例
    const wordExportService = new WordExportService();

    // 读取简单测试HTML文件
    const htmlFilePath = path.join(
      process.cwd(),
      'testFiles',
      'simple-test.html'
    );
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf-8');
    console.log(`已读取HTML文件: ${htmlFilePath}`);

    // 设置输出路径
    const outputPath = path.join(
      process.cwd(),
      'testFiles',
      'simple-test-result.docx'
    );

    // 设置导出选项
    const exportOptions = {
      title: '简单测试文档',
      author: '测试系统',
      orientation: 'portrait' as const,
    };

    // 将HTML内容转换为Word文档并保存
    console.log('开始转换HTML到Word...');
    const savedPath = await wordExportService.exportHtmlToWordFile(
      htmlContent,
      outputPath,
      exportOptions
    );

    console.log(`Word文档保存成功: ${savedPath}`);
    console.log('简单测试完成！');
  } catch (error) {
    console.error('简单Word导出测试失败:', error);
  }
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

/**
 * 规范化处理流程测试
 * 测试新的两步处理流程：html-to-docx + docx库二次处理
 */
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function testStandardizedProcess() {
  console.log('\n=== 规范化处理流程测试 ===');
  
  const wordExportService = new WordExportService();
  
  const testHtml = `
    <html>
      <body>
        <h1>规范化处理流程测试</h1>
        
        <h2>一、着重号测试（二次处理）</h2>
        <p><strong>1. 下面对有新疆房的语言的理解，错误的一项是（　　）</strong></p>
        <p>A. A. 边疆（<span data-emphasis-mark="dot">重返国界的领土</span>）</p>
        <p>B. B. 绚丽多彩（<span data-emphasis-mark="dot">各种各样的色彩</span>）</p>
        <p>C. C. 坪坝（<span data-emphasis-mark="dot">高高低低的坝地</span>）</p>
        <p>D. D. 高高低低工厂对有新疆房的可了的</p>
        
        <h2>二、表格测试（html-to-docx处理）</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th style="background-color: #f0f0f0; padding: 8px;">题型</th>
            <th style="background-color: #f0f0f0; padding: 8px;">内容</th>
            <th style="background-color: #f0f0f0; padding: 8px;">着重号测试</th>
          </tr>
          <tr>
            <td style="padding: 8px;">选择题</td>
            <td style="padding: 8px;">普通文字内容</td>
            <td style="padding: 8px;"><span data-emphasis-mark="dot">着重号文字</span></td>
          </tr>
          <tr>
            <td style="padding: 8px;">填空题</td>
            <td style="padding: 8px;">更多测试内容</td>
            <td style="padding: 8px;">无着重号</td>
          </tr>
          <tr>
            <td style="padding: 8px;">判断题</td>
            <td style="padding: 8px;"><span data-emphasis-mark="dot">表格内着重号</span></td>
            <td style="padding: 8px;">混合内容</td>
          </tr>
        </table>
        
        <h2>三、复杂格式测试（html-to-docx处理）</h2>
        <p style="text-align: center; color: blue; font-size: 16px;">
          居中蓝色段落，包含<span data-emphasis-mark="dot">着重号文字</span>，测试格式保持。
        </p>
        <p style="text-align: right; font-weight: bold;">
          右对齐粗体段落，包含<span data-emphasis-mark="dot">更多着重号</span>。
        </p>
        
        <h2>四、列表测试（html-to-docx处理）</h2>
        <ul>
          <li>列表项1：<span data-emphasis-mark="dot">着重号内容</span></li>
          <li>列表项2：普通内容</li>
          <li>列表项3：混合<span data-emphasis-mark="dot">着重号</span>和普通文字</li>
        </ul>
        
        <ol>
          <li>有序列表1：<span data-emphasis-mark="dot">数字着重号</span></li>
          <li>有序列表2：正常内容</li>
        </ol>
        
        <h2>五、图片测试（html-to-docx处理）</h2>
        <p>这里应该有图片，但我们主要测试文字处理。</p>
        <p>包含<span data-emphasis-mark="dot">着重号</span>的图片说明文字。</p>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'standardized-process-test.docx');
    
    // 使用新的规范化流程
    await wordExportService.exportHtmlToWordFile(testHtml, outputPath, {
      title: '规范化处理流程测试',
      author: '测试系统',
      postProcessorOptions: {
        processEmphasisMarks: true, // 启用着重号二次处理
        processSpecialFonts: false, // 暂不处理特殊字体
        processSpecialColors: false, // 暂不处理特殊颜色
      }
    });
    
    console.log('✅ 规范化处理流程测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('\n🔧 处理流程：');
    console.log('1. HTML预处理：清理和优化HTML结构');
    console.log('2. html-to-docx基础转换：处理表格、列表、段落等');
    console.log('3. docx库二次处理：专门处理着重号等特殊样式');
    console.log('\n📋 预期效果：');
    console.log('- 表格：html-to-docx完整处理，格式完美');
    console.log('- 段落：html-to-docx完整处理，对齐和颜色保持');
    console.log('- 列表：html-to-docx完整处理，结构完整');
    console.log('- 着重号：docx库二次处理，专业效果');
    return true;
  } catch (error) {
    console.log('❌ 规范化处理流程测试失败:', error.message);
    return false;
  }
}

async function testComplexDocumentStandardized() {
  console.log('\n=== 复杂文档规范化处理测试 ===');
  
  const wordExportService = new WordExportService();
  
  try {
    // 读取原始的复杂HTML文件
    const fs = require('fs');
    const htmlFilePath = path.join(process.cwd(), 'testFiles', 'input-html-sample.html');
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf-8');
    console.log(`已读取HTML文件: ${htmlFilePath}`);

    const outputPath = path.join(process.cwd(), 'testFiles', 'complex-standardized.docx');
    
    // 使用规范化流程处理复杂文档
    await wordExportService.exportHtmlToWordFile(htmlContent, outputPath, {
      title: '复杂文档规范化处理',
      author: '测试系统',
      postProcessorOptions: {
        processEmphasisMarks: true, // 处理着重号
        processSpecialFonts: false, // 预留扩展
        processSpecialColors: false, // 预留扩展
      }
    });
    
    console.log('✅ 复杂文档规范化处理测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：');
    console.log('- 所有表格、段落、列表格式完整保持');
    console.log('- 着重号通过二次处理正确显示');
    console.log('- 整体文档结构和样式完美');
    return true;
  } catch (error) {
    console.log('❌ 复杂文档规范化处理测试失败:', error.message);
    return false;
  }
}

async function testNoEmphasisStandardized() {
  console.log('\n=== 无着重号规范化处理测试 ===');
  
  const wordExportService = new WordExportService();
  
  const testHtml = `
    <html>
      <body>
        <h1>无着重号文档测试</h1>
        
        <h2>一、普通内容</h2>
        <p>这是普通段落，没有着重号。</p>
        <p><strong>这是粗体段落</strong>，也没有着重号。</p>
        <p style="color: red;">这是红色段落。</p>
        
        <h2>二、表格测试</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th style="background-color: #f0f0f0;">标题1</th>
            <th style="background-color: #f0f0f0;">标题2</th>
          </tr>
          <tr>
            <td>内容1</td>
            <td>内容2</td>
          </tr>
        </table>
        
        <h2>三、列表测试</h2>
        <ul>
          <li>列表项1</li>
          <li>列表项2</li>
        </ul>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'no-emphasis-standardized.docx');
    
    // 无着重号文档，二次处理应该跳过
    await wordExportService.exportHtmlToWordFile(testHtml, outputPath, {
      title: '无着重号规范化处理测试',
      author: '测试系统',
      postProcessorOptions: {
        processEmphasisMarks: true, // 启用但应该跳过
      }
    });
    
    console.log('✅ 无着重号规范化处理测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：');
    console.log('- 二次处理检测到无着重号，直接返回html-to-docx结果');
    console.log('- 所有格式完整保持');
    console.log('- 处理效率最优');
    return true;
  } catch (error) {
    console.log('❌ 无着重号规范化处理测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('开始规范化处理流程测试...');
  
  const tests = [
    testStandardizedProcess,
    testComplexDocumentStandardized,
    testNoEmphasisStandardized,
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    const result = await test();
    if (result) {
      passedTests++;
    }
  }
  
  console.log('\n=== 规范化处理流程测试总结 ===');
  console.log(`总测试数: ${tests.length}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${tests.length - passedTests}`);
  
  if (passedTests === tests.length) {
    console.log('🎉 规范化处理流程测试通过！');
    console.log('\n🏗️ 规范化架构优势：');
    console.log('1. 清晰的两步处理流程');
    console.log('2. html-to-docx处理大部分内容（表格、段落、列表等）');
    console.log('3. docx库二次处理特殊样式（着重号等）');
    console.log('4. 易于扩展新的特殊样式处理');
    console.log('5. 保持html-to-docx的完整功能');
    console.log('\n这是一个规范、可扩展的处理架构！');
  } else {
    console.log('⚠️  部分规范化处理流程测试失败，请检查相关功能。');
  }
  
  console.log('\n📁 生成的测试文件：');
  console.log('- standardized-process-test.docx - 规范化流程完整测试');
  console.log('- complex-standardized.docx - 复杂文档规范化处理');
  console.log('- no-emphasis-standardized.docx - 无着重号规范化处理');
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

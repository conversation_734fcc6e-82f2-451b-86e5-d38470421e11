import { Provide } from '@midwayjs/core';
import * as fs from 'fs';
import * as path from 'path';
import * as HTMLtoDOCX from 'html-to-docx';
import {
  preprocessHtml,
  PreprocessOptions,
} from '../utils/word-export/html-preprocessor';
import {
  postprocessWord,
  PostprocessOptions,
} from '../utils/word-export/word-postprocessor';
import {
  saveProcessedHtml,
  logConversionInfo,
} from '../utils/word-export/debug-helper';
import {
  processEmphasisInHtml,
  EmphasisProcessorOptions,
} from '../utils/word-export/emphasis-processor';

/**
 * Word导出服务
 * 提供将HTML内容转换为Word文档的功能
 */
@Provide()
export class WordExportService {
  private readonly OUTPUT_DIR = path.join(
    process.cwd(),
    'temp',
    'word-exports'
  );

  // 默认页边距（单位：缇，1厘米 = 567缇）
  private readonly DEFAULT_MARGINS = {
    top: 1440, // 2.54厘米
    right: 1440, // 2.54厘米
    bottom: 1440, // 2.54厘米
    left: 1440, // 2.54厘米
  };

  constructor() {
    // 确保输出目录存在
    if (!fs.existsSync(this.OUTPUT_DIR)) {
      fs.mkdirSync(this.OUTPUT_DIR, { recursive: true });
    }
  }

  /**
   * 将HTML内容转换为Word文档
   * @param htmlContent HTML内容
   * @param options 导出选项
   * @returns Word文档的Buffer
   */
  async exportHtmlToWord(
    htmlContent: string,
    options: {
      title?: string;
      author?: string;
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
      };
      orientation?: 'portrait' | 'landscape';
      preprocessOptions?: PreprocessOptions;
      postprocessOptions?: PostprocessOptions;
      useDocxForEmphasis?: boolean; // 是否使用docx库处理着重号
      emphasisOptions?: EmphasisProcessorOptions;
    } = {}
  ): Promise<Buffer> {
    try {
      console.log('开始HTML转Word转换...');

      // 检查是否使用docx库处理着重号
      if (options.useDocxForEmphasis) {
        console.log('使用docx库处理着重号...');

        // 第一步：HTML预处理（不处理着重号）
        console.log('步骤1: HTML预处理（跳过着重号处理）');
        const preprocessedHtml = preprocessHtml(htmlContent, {
          ...options.preprocessOptions,
          processEmphasisMarks: false, // 跳过着重号预处理
        });

        // 第二步：使用docx库直接处理着重号
        console.log('步骤2: 使用docx库处理着重号');
        const finalBuffer = await processEmphasisInHtml(
          preprocessedHtml,
          options.emphasisOptions
        );

        console.log('使用docx库的HTML转Word转换完成');
        return finalBuffer;
      }

      // 标准处理流程
      // 第一步：HTML预处理
      console.log('步骤1: HTML预处理');
      const preprocessedHtml = preprocessHtml(
        htmlContent,
        options.preprocessOptions
      );

      // 保存预处理后的HTML用于调试
      saveProcessedHtml(preprocessedHtml);
      logConversionInfo('预处理完成', {
        originalLength: htmlContent.length,
        processedLength: preprocessedHtml.length,
      });

      // 第二步：使用html-to-docx进行基础转换
      console.log('步骤2: 使用html-to-docx进行基础转换');
      const docxOptions = {
        table: { row: { cantSplit: true } },
        footer: true,
        pageNumber: true,
        margins: options.margins || this.DEFAULT_MARGINS,
        orientation: options.orientation || 'portrait',
        title: options.title || '文档',
        creator: options.author || '系统',
      };

      const docxBuffer = await HTMLtoDOCX(preprocessedHtml, null, docxOptions);

      // 第三步：Word文档后处理
      console.log('步骤3: Word文档后处理');
      const finalBuffer = await postprocessWord(docxBuffer, {
        ...options.postprocessOptions,
        originalHtmlContent: htmlContent, // 传递原始HTML内容
      });

      console.log('HTML转Word转换完成');
      return finalBuffer;
    } catch (error) {
      console.error('HTML转Word转换失败:', error);
      throw new Error(`HTML转Word转换失败: ${error.message}`);
    }
  }

  /**
   * 将HTML内容转换为Word文档并保存到文件
   * @param htmlContent HTML内容
   * @param outputPath 输出文件路径
   * @param options 导出选项
   * @returns 保存的文件路径
   */
  async exportHtmlToWordFile(
    htmlContent: string,
    outputPath: string,
    options: {
      title?: string;
      author?: string;
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
      };
      orientation?: 'portrait' | 'landscape';
      preprocessOptions?: PreprocessOptions;
      postprocessOptions?: PostprocessOptions;
      useDocxForEmphasis?: boolean;
      emphasisOptions?: EmphasisProcessorOptions;
    } = {}
  ): Promise<string> {
    try {
      const docxBuffer = await this.exportHtmlToWord(htmlContent, options);
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      await fs.promises.writeFile(outputPath, docxBuffer);
      return outputPath;
    } catch (error) {
      console.error('保存Word文档失败:', error);
      throw error;
    }
  }

  /**
   * 从本地HTML文件导出Word文档到临时目录
   * @param htmlFilePath HTML文件路径
   * @param options 导出选项
   * @returns 生成的Word文档路径
   */
  async exportLocalHtmlToTemp(
    htmlFilePath: string,
    options: {
      title?: string;
      author?: string;
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
      };
      orientation?: 'portrait' | 'landscape';
    } = {}
  ): Promise<string> {
    try {
      // 读取HTML文件内容
      const htmlContent = await fs.promises.readFile(htmlFilePath, 'utf-8');

      // 生成输出文件名
      const baseName = path.basename(htmlFilePath, path.extname(htmlFilePath));
      const outputPath = path.join(this.OUTPUT_DIR, `${baseName}.docx`);

      // 转换并保存
      return await this.exportHtmlToWordFile(htmlContent, outputPath, options);
    } catch (error) {
      console.error('从本地HTML文件导出Word失败:', error);
      throw error;
    }
  }
}

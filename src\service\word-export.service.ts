import { Provide } from '@midwayjs/core';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Word导出服务
 * 提供将HTML内容转换为Word文档的功能
 */
@Provide()
export class WordExportService {
  private readonly NODE_TYPES = {
    ELEMENT_NODE: 1,
    TEXT_NODE: 3,
  };

  private readonly OUTPUT_DIR = path.join(
    process.cwd(),
    'temp',
    'word-exports'
  );

  // 默认页边距（单位：缇，1厘米 = 567缇）
  private readonly DEFAULT_MARGINS = {
    top: 1440, // 2.54厘米
    right: 1440, // 2.54厘米
    bottom: 1440, // 2.54厘米
    left: 1440, // 2.54厘米
  };

  private readonly DEFAULT_PAGE_WIDTH_CM = 21; // A4纸宽度，单位：cm
  private readonly CM_TO_PX = 37.8; // 1cm = 37.8px

  private calculatePageWidth(margins: {
    left?: number;
    right?: number;
  }): number {
    // 页边距单位为 twip，需转为 cm
    const leftMargin = margins.left ?? this.DEFAULT_MARGINS.left;
    const rightMargin = margins.right ?? this.DEFAULT_MARGINS.right;
    // 页边距总和（cm）
    const marginCm = (leftMargin + rightMargin) / 567;
    // 最大可用宽度（cm）
    const maxWidthCm = this.DEFAULT_PAGE_WIDTH_CM - marginCm;
    // 返回像素值
    return maxWidthCm * this.CM_TO_PX;
  }

  constructor() {
    // 确保输出目录存在
    if (!fs.existsSync(this.OUTPUT_DIR)) {
      fs.mkdirSync(this.OUTPUT_DIR, { recursive: true });
    }
  }

  /**
   * 将HTML内容转换为Word文档
   * @param htmlContent HTML内容
   * @returns Word文档的Buffer
   */
  async exportHtmlToWord(htmlContent: string): Promise<Buffer> {
    return null;
  }

  /**
   * 将HTML内容转换为Word文档并保存到文件
   * @param htmlContent HTML内容
   * @param outputPath 输出文件路径
   * @returns 保存的文件路径
   */
  async exportHtmlToWordFile(
    htmlContent: string,
    outputPath: string
  ): Promise<string> {
    try {
      const docxBuffer = await this.exportHtmlToWord(htmlContent);
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      await fs.promises.writeFile(outputPath, docxBuffer);
      return outputPath;
    } catch (error) {
      console.error('保存Word文档失败:', error);
      throw error;
    }
  }
}

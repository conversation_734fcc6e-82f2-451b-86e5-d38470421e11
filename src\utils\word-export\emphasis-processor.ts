/**
 * 着重号处理器
 * 使用docx库来处理html-to-docx无法正确处理的着重号
 */
import { Document, Packer, Paragraph, TextRun, UnderlineType } from 'docx';
import { JSDOM } from 'jsdom';

export interface EmphasisProcessorOptions {
  /** 着重号样式：dot（点）、comma（逗号）、circle（圆圈） */
  emphasisStyle?: 'dot' | 'comma' | 'circle';
  /** 是否使用下划线模拟着重号 */
  useUnderlineAsEmphasis?: boolean;
  /** 是否使用粗体增强效果 */
  useBoldForEmphasis?: boolean;
}

/**
 * 着重号处理器
 */
export class EmphasisProcessor {
  private htmlContent: string;
  private options: EmphasisProcessorOptions;

  constructor(htmlContent: string, options: EmphasisProcessorOptions = {}) {
    this.htmlContent = htmlContent;
    this.options = {
      emphasisStyle: 'dot',
      useUnderlineAsEmphasis: true,
      useBoldForEmphasis: true,
      ...options,
    };
  }

  /**
   * 处理着重号，生成新的Word文档
   */
  async processEmphasisMarks(): Promise<Buffer> {
    console.log('开始使用docx库处理着重号...');

    // 解析HTML内容
    const dom = new JSDOM(this.htmlContent);
    const document = dom.window.document;

    // 查找所有带有着重号标记的元素（包括data-emphasis-mark属性和emphasis-mark类）
    const emphasisElements = document.querySelectorAll('[data-emphasis-mark], .emphasis-mark');
    console.log(`找到 ${emphasisElements.length} 个着重号元素`);

    // 创建Word文档段落
    const paragraphs: Paragraph[] = [];

    // 处理文档中的所有段落
    const htmlParagraphs = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6');
    
    htmlParagraphs.forEach((htmlP, index) => {
      console.log(`处理段落 ${index + 1}: ${htmlP.textContent?.substring(0, 50)}...`);
      
      const runs: TextRun[] = [];
      
      // 递归处理段落中的所有节点
      this.processNode(htmlP, runs);
      
      if (runs.length > 0) {
        const paragraph = new Paragraph({
          children: runs,
        });
        paragraphs.push(paragraph);
      }
    });

    // 如果没有找到段落，创建一个默认段落
    if (paragraphs.length === 0) {
      const bodyText = document.body.textContent || '无内容';
      const paragraph = new Paragraph({
        children: [
          new TextRun({
            text: bodyText,
          }),
        ],
      });
      paragraphs.push(paragraph);
    }

    // 创建Word文档
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: paragraphs,
        },
      ],
    });

    // 生成文档Buffer
    const buffer = await Packer.toBuffer(doc);
    console.log('使用docx库处理着重号完成');
    
    return buffer;
  }

  /**
   * 递归处理HTML节点，转换为TextRun
   */
  private processNode(node: Node, runs: TextRun[]): void {
    if (node.nodeType === 3) { // 文本节点
      const text = node.textContent || '';
      if (text.trim()) {
        runs.push(new TextRun({ text }));
      }
    } else if (node.nodeType === 1) { // 元素节点
      const element = node as Element;
      
      if (element.classList.contains('emphasis-mark') || element.hasAttribute('data-emphasis-mark')) {
        // 处理着重号元素
        const text = element.textContent || '';
        if (text.trim()) {
          const emphasisRun = this.createEmphasisTextRun(text);
          runs.push(emphasisRun);
        }
      } else {
        // 处理其他元素的样式
        const text = element.textContent || '';
        if (text.trim()) {
          const styledRun = this.createStyledTextRun(text, element);
          runs.push(styledRun);
        }
      }
    }
  }

  /**
   * 创建着重号文本运行
   */
  private createEmphasisTextRun(text: string): TextRun {
    const runOptions: any = {
      text,
    };

    // 使用下划线模拟着重号
    if (this.options.useUnderlineAsEmphasis) {
      runOptions.underline = {
        type: UnderlineType.DOTTED,
        color: '000000',
      };
    }

    // 使用粗体增强效果
    if (this.options.useBoldForEmphasis) {
      runOptions.bold = true;
    }

    // 添加高亮背景色
    runOptions.highlight = 'yellow';

    console.log(`创建着重号文本: "${text}"`);
    
    return new TextRun(runOptions);
  }

  /**
   * 创建带样式的文本运行
   */
  private createStyledTextRun(text: string, element: Element): TextRun {
    const runOptions: any = {
      text,
    };

    const style = (element as HTMLElement).style;

    // 处理粗体
    if (style.fontWeight === 'bold' || element.tagName.match(/^H[1-6]$/)) {
      runOptions.bold = true;
    }

    // 处理斜体
    if (style.fontStyle === 'italic') {
      runOptions.italics = true;
    }

    // 处理下划线
    if (style.textDecoration?.includes('underline')) {
      runOptions.underline = {
        type: UnderlineType.SINGLE,
      };
    }

    // 处理颜色
    if (style.color) {
      // 简单的颜色转换
      const color = style.color.replace('#', '');
      if (color.length === 6) {
        runOptions.color = color;
      }
    }

    return new TextRun(runOptions);
  }
}

/**
 * 处理HTML中的着重号，生成Word文档
 * @param htmlContent HTML内容
 * @param options 处理选项
 * @returns Word文档Buffer
 */
export async function processEmphasisInHtml(
  htmlContent: string,
  options: EmphasisProcessorOptions = {}
): Promise<Buffer> {
  const processor = new EmphasisProcessor(htmlContent, options);
  return await processor.processEmphasisMarks();
}

/**
 * 混合方案测试：html-to-docx + docx库后处理着重号
 * 验证二合一方案的效果
 */
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function testHybridEmphasisProcessing() {
  console.log('\n=== 测试混合方案：html-to-docx + docx库后处理 ===');
  
  const wordExportService = new WordExportService();
  
  const emphasisHtml = `
    <html>
      <body>
        <h1>混合方案着重号测试</h1>
        <p>这里有<span style="text-emphasis: filled currentColor; text-emphasis-position: under right;" data-emphasis-mark="dot">着重号文字1</span>。</p>
        <p>还有<span data-emphasis-mark="dot" style="text-emphasis: filled currentColor;">着重号文字2</span>。</p>
        <p>以及<span data-emphasis-mark="dot">仅有属性的着重号</span>。</p>
        <p>普通文字没有着重号。</p>
        
        <table style="width: 100%;">
          <tr>
            <th>标题1</th>
            <th>标题2</th>
          </tr>
          <tr>
            <td>内容1</td>
            <td>内容2</td>
          </tr>
        </table>
        
        <p>预期效果：先用html-to-docx处理表格等复杂结构，再用docx库处理着重号</p>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'hybrid-emphasis-test.docx');
    
    // 使用混合方案：启用后处理中的着重号处理
    await wordExportService.exportHtmlToWordFile(emphasisHtml, outputPath, {
      title: '混合方案着重号测试',
      author: '测试系统',
      useDocxForEmphasis: false, // 使用标准html-to-docx流程
      postprocessOptions: {
        processEmphasisMarks: true, // 启用docx库后处理着重号
      }
    });
    
    console.log('✅ 混合方案着重号处理测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：');
    console.log('- 表格：html-to-docx处理，保持完整结构');
    console.log('- 着重号：docx库后处理，专业的下划线+粗体+高亮效果');
    console.log('- 其他格式：html-to-docx处理，保持原有样式');
    return true;
  } catch (error) {
    console.log('❌ 混合方案着重号处理测试失败:', error.message);
    return false;
  }
}

async function testHybridVsStandard() {
  console.log('\n=== 对比测试：混合方案 vs 标准方案 ===');
  
  const wordExportService = new WordExportService();
  
  const testHtml = `
    <html>
      <body>
        <h1>对比测试</h1>
        <p>这是<span data-emphasis-mark="dot" style="text-emphasis: filled currentColor;">着重号文字</span>的测试。</p>
        <p>普通文字和<strong>粗体文字</strong>。</p>
      </body>
    </html>
  `;
  
  try {
    // 标准方案
    const standardPath = path.join(process.cwd(), 'testFiles', 'standard-vs-hybrid-standard.docx');
    await wordExportService.exportHtmlToWordFile(testHtml, standardPath, {
      title: '标准方案',
      postprocessOptions: {
        processEmphasisMarks: false, // 不启用后处理
      }
    });
    
    // 混合方案
    const hybridPath = path.join(process.cwd(), 'testFiles', 'standard-vs-hybrid-hybrid.docx');
    await wordExportService.exportHtmlToWordFile(testHtml, hybridPath, {
      title: '混合方案',
      postprocessOptions: {
        processEmphasisMarks: true, // 启用docx库后处理
      }
    });
    
    console.log('✅ 对比测试完成');
    console.log(`标准方案文档: ${standardPath}`);
    console.log(`混合方案文档: ${hybridPath}`);
    console.log('请手动打开两个文档对比着重号效果！');
    return true;
  } catch (error) {
    console.log('❌ 对比测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('开始混合方案着重号处理测试...');
  
  const tests = [
    testHybridEmphasisProcessing,
    testHybridVsStandard,
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    const result = await test();
    if (result) {
      passedTests++;
    }
  }
  
  console.log('\n=== 混合方案测试总结 ===');
  console.log(`总测试数: ${tests.length}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${tests.length - passedTests}`);
  
  if (passedTests === tests.length) {
    console.log('🎉 混合方案测试通过！');
    console.log('\n📋 混合方案优势：');
    console.log('1. 保留html-to-docx的完整功能（表格、图片、复杂布局）');
    console.log('2. 使用docx库专门处理着重号，效果更专业');
    console.log('3. 一个方案解决所有问题，无需选择');
    console.log('\n这就是您要的二合一方案！');
  } else {
    console.log('⚠️  部分混合方案测试失败，请检查相关功能。');
  }
  
  console.log('\n📁 生成的测试文件：');
  console.log('- hybrid-emphasis-test.docx - 混合方案完整测试');
  console.log('- standard-vs-hybrid-standard.docx - 标准方案对比');
  console.log('- standard-vs-hybrid-hybrid.docx - 混合方案对比');
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

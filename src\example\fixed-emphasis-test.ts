/**
 * 修复后的着重号测试
 * 验证着重号效果改进和格式保持
 */
import * as fs from 'fs';
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function testFixedEmphasisProcessing() {
  console.log('\n=== 测试修复后的着重号处理 ===');
  
  const wordExportService = new WordExportService();
  
  const testHtml = `
    <html>
      <body>
        <h1>修复后的着重号测试</h1>
        
        <h2>一、选择题</h2>
        <p><strong>1. 下面对有新疆房的语言的理解，错误的一项是（　　）</strong></p>
        <p>A. A. 边疆（<span data-emphasis-mark="dot" style="text-emphasis: filled currentColor;">重返国界的领土</span>）</p>
        <p>B. B. 绚丽多彩（<span data-emphasis-mark="dot">各种各样的色彩加以美丽</span>）</p>
        <p>C. C. 坪坝（<span data-emphasis-mark="dot">高高低低的坝地</span>）</p>
        <p>D. D. 高高低低工厂对有新疆房的可了的</p>
        
        <h2>二、表格测试</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th>题型</th>
            <th>内容</th>
            <th>着重号</th>
          </tr>
          <tr>
            <td>选择题</td>
            <td>普通文字</td>
            <td><span data-emphasis-mark="dot">着重号文字</span></td>
          </tr>
          <tr>
            <td>填空题</td>
            <td>更多内容</td>
            <td>无着重号</td>
          </tr>
        </table>
        
        <h2>三、段落格式测试</h2>
        <p style="text-align: center; font-size: 16px;">居中的段落，包含<span data-emphasis-mark="dot">着重号文字</span>。</p>
        <p style="text-align: right; color: blue;">右对齐的蓝色段落。</p>
        
        <h2>四、列表测试</h2>
        <ul>
          <li>列表项1：<span data-emphasis-mark="dot">着重号内容</span></li>
          <li>列表项2：普通内容</li>
          <li>列表项3：混合<span data-emphasis-mark="dot">着重号</span>和普通文字</li>
        </ul>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'fixed-emphasis-test.docx');
    
    // 使用标准方案，不启用后处理（避免破坏格式）
    await wordExportService.exportHtmlToWordFile(testHtml, outputPath, {
      title: '修复后的着重号测试',
      author: '测试系统',
      postprocessOptions: {
        processEmphasisMarks: false, // 不启用后处理，保持格式完整
      }
    });
    
    console.log('✅ 修复后的着重号处理测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：');
    console.log('- 着重号：改进的点状下划线效果');
    console.log('- 表格：完整保持格式');
    console.log('- 段落：保持对齐和颜色');
    console.log('- 列表：保持结构');
    return true;
  } catch (error) {
    console.log('❌ 修复后的着重号处理测试失败:', error.message);
    return false;
  }
}

async function testComplexDocumentFixed() {
  console.log('\n=== 测试复杂文档的修复效果 ===');
  
  const wordExportService = new WordExportService();
  
  try {
    // 读取原始的复杂HTML文件
    const htmlFilePath = path.join(process.cwd(), 'testFiles', 'input-html-sample.html');
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf-8');
    console.log(`已读取HTML文件: ${htmlFilePath}`);

    const outputPath = path.join(process.cwd(), 'testFiles', 'complex-fixed-result.docx');
    
    // 使用标准方案，保持所有格式
    await wordExportService.exportHtmlToWordFile(htmlContent, outputPath, {
      title: '复杂文档修复测试',
      author: '测试系统',
      postprocessOptions: {
        processEmphasisMarks: false, // 不启用后处理，保持格式完整
      }
    });
    
    console.log('✅ 复杂文档修复测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：');
    console.log('- 所有格式保持完整');
    console.log('- 着重号使用改进的样式');
    console.log('- 表格、段落、列表等结构完整');
    return true;
  } catch (error) {
    console.log('❌ 复杂文档修复测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('开始修复后的着重号测试...');
  
  const tests = [
    testFixedEmphasisProcessing,
    testComplexDocumentFixed,
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    const result = await test();
    if (result) {
      passedTests++;
    }
  }
  
  console.log('\n=== 修复测试总结 ===');
  console.log(`总测试数: ${tests.length}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${tests.length - passedTests}`);
  
  if (passedTests === tests.length) {
    console.log('🎉 修复测试通过！');
    console.log('\n📋 修复方案特点：');
    console.log('1. 改进着重号样式：使用点状下划线代替实线');
    console.log('2. 保持所有格式：不使用破坏性的后处理');
    console.log('3. 完整功能支持：表格、段落、列表等全部保持');
    console.log('4. 简化实现：回到稳定的html-to-docx方案');
  } else {
    console.log('⚠️  部分修复测试失败，请检查相关功能。');
  }
  
  console.log('\n📁 生成的测试文件：');
  console.log('- fixed-emphasis-test.docx - 修复后的着重号测试');
  console.log('- complex-fixed-result.docx - 复杂文档修复测试');
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

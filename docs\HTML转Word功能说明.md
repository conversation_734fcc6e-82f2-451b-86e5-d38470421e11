# HTML转Word功能说明

## 功能概述

本功能是对现有Word转HTML功能的逆向实现，可以将结构化的HTML试题内容转换为Word文档进行导出。主要特点包括：

- 支持将HTML内容转换为标准的.docx格式文档
- 保留原始HTML中的格式、样式、图片、表格和公式等元素
- 提供API接口和示例代码，方便集成到现有系统中
- 支持自定义文档属性（标题、作者、页边距、方向等）

## 使用方法

### 1. 通过API接口使用

系统提供了两个API接口用于HTML转Word功能：

#### 1.1 直接下载Word文档

```
POST /word-export/html-to-word
```

请求参数：

```json
{
  "htmlContent": "<p>HTML内容</p>",
  "options": {
    "title": "文档标题",
    "author": "作者",
    "fileName": "输出文件名.docx",
    "margins": {
      "top": 1440,
      "right": 1440,
      "bottom": 1440,
      "left": 1440
    },
    "orientation": "portrait" // 或 "landscape"
  }
}
```

响应：直接返回Word文档的二进制流，浏览器会自动下载。

#### 1.2 保存到服务器

```
POST /word-export/save-to-server
```

请求参数：

```json
{
  "htmlContent": "<p>HTML内容</p>",
  "outputPath": "/path/to/output.docx",
  "options": {
    "title": "文档标题",
    "author": "作者",
    "fileName": "输出文件名.docx",
    "margins": {
      "top": 1440,
      "right": 1440,
      "bottom": 1440,
      "left": 1440
    },
    "orientation": "portrait" // 或 "landscape"
  }
}
```

响应：

```json
{
  "success": true,
  "message": "Word文档保存成功",
  "data": {
    "filePath": "/path/to/output.docx"
  }
}
```

### 2. 在代码中使用

可以直接在代码中使用`WordExportService`进行HTML到Word的转换：

```typescript
import { WordExportService } from '../service/word-export.service';

// 创建服务实例
const wordExportService = new WordExportService();

// 将HTML转换为Word文档Buffer
const docxBuffer = await wordExportService.exportHtmlToWord(
  htmlContent,
  {
    title: '文档标题',
    author: '作者',
    orientation: 'portrait'
  }
);

// 或者直接保存为文件
const savedPath = await wordExportService.exportHtmlToWordFile(
  htmlContent,
  '/path/to/output.docx',
  {
    title: '文档标题',
    author: '作者',
    orientation: 'portrait'
  }
);
```

### 3. 运行示例代码

项目中提供了示例代码，可以直接运行测试：

```bash
# 进入项目目录
cd question-import-word

# 编译TypeScript
npm run build

# 运行示例
node dist/example/word-export-example.js
```

示例代码会读取`testFiles/input-html-sample.html`文件，将其转换为Word文档并保存为`testFiles/output-word-result.docx`。

**快速验证方法：**

您也可以使用以下命令直接运行 TypeScript 示例文件，无需先进行编译：

```bash
node --require ts-node/register src/example/word-export-example.ts
```

或者使用 npx 方式：

```bash
npx ts-node src/example/word-export-example.ts
```

## 实现原理

### 技术栈

- **核心库**：html-to-docx（主要转换引擎）
- **DOM解析**：jsdom（HTML预处理）
- **后处理**：docx（高级Word文档操作，预留）
- **文件操作**：fs、path

### 处理流程

本实现采用**三步处理流程**，确保最佳的转换效果：

#### 第一步：HTML预处理
- **着重号处理**：将CSS着重号样式转换为下划线样式
- **布局优化**：处理display:inline-block元素，确保正确布局
- **浮动元素清理**：移除position:absolute等可能导致问题的样式
- **表格重建**：完全重建表格结构，移除可能导致XML错误的属性
- **样式清理**：移除html-to-docx不支持的CSS属性

#### 第二步：html-to-docx基础转换
- 使用html-to-docx库进行主要的HTML到Word转换
- 应用文档属性（标题、作者、页边距、方向等）
- 处理基本的文本格式、表格、列表等元素

#### 第三步：Word文档后处理（预留）
- 为将来可能需要的高级处理预留接口
- 可以使用docx库进行更精细的Word文档操作
- 处理html-to-docx无法处理的特殊格式需求

### 注意事项

1. **着重号处理**：
   - CSS着重号（text-emphasis）自动转换为下划线样式
   - 保持文本的强调效果，确保在Word中正确显示

2. **表格处理**：
   - 自动重建表格结构，移除可能导致问题的属性
   - 统一设置边框和内边距，确保在Word中正确显示
   - 支持表格的基本样式和对齐方式

3. **图片处理**：
   - 支持base64编码的内联图片
   - 外部图片链接可能无法正确转换

4. **样式处理**：
   - 支持基本的文本样式（字体、颜色、对齐方式等）
   - 自动清理可能导致转换问题的CSS属性
   - 复杂的CSS样式可能无法完全转换

5. **布局处理**：
   - 自动处理display:inline-block等布局样式
   - 移除浮动定位等可能导致问题的样式

## 限制和已知问题

1. 不支持所有CSS样式，只支持Word文档能够表示的基本样式
2. 复杂的布局可能无法完全保留
3. 某些特殊字体可能会被替换为标准字体
4. 非图片形式的数学公式可能无法正确显示

## 未来改进

1. 增强对复杂布局的支持
2. 改进公式处理，支持MathML或LaTeX格式的公式
3. 添加更多自定义选项（水印、页眉页脚等）
4. 优化图片处理，支持外部图片的自动下载和转换

## 参考资料

- [html-to-docx文档](https://www.npmjs.com/package/html-to-docx)
- [Word文档格式规范](https://docs.microsoft.com/en-us/office/open-xml/word-processing)
/**
 * 改进功能测试
 * 专门测试着重号和表格的改进效果
 */
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function testImprovedEmphasisMarks() {
  console.log('\n=== 测试改进的着重号处理 ===');
  
  const wordExportService = new WordExportService();
  
  const emphasisHtml = `
    <html>
      <body>
        <h1>着重号改进测试</h1>
        <p>这里有<span style="text-emphasis: filled currentColor; text-emphasis-position: under right;" data-emphasis-mark="dot">着重号文字1</span>。</p>
        <p>还有<span data-emphasis-mark="dot" style="text-emphasis: filled currentColor;">着重号文字2</span>。</p>
        <p>以及<span data-emphasis-mark="dot">仅有属性的着重号</span>。</p>
        <p>预期效果：粗体+黄色背景+下划线</p>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'improved-emphasis-test.docx');
    await wordExportService.exportHtmlToWordFile(emphasisHtml, outputPath, {
      title: '改进的着重号测试',
      author: '测试系统'
    });
    console.log('✅ 改进的着重号处理测试通过');
    console.log('   预期效果：粗体+黄色背景+下划线');
    return true;
  } catch (error) {
    console.log('❌ 改进的着重号处理测试失败:', error.message);
    return false;
  }
}

async function testImprovedTable() {
  console.log('\n=== 测试改进的表格处理 ===');
  
  const wordExportService = new WordExportService();
  
  const tableHtml = `
    <html>
      <body>
        <h1>表格改进测试</h1>
        <table style="width: 100%;">
          <tbody>
            <tr>
              <th width="33%">表头1</th>
              <th width="33%">表头2</th>
              <th width="34%">表头3</th>
            </tr>
            <tr>
              <td style="width: 33%;">内容1</td>
              <td style="width: 33%;">内容2</td>
              <td style="width: 34%;">内容3</td>
            </tr>
            <tr>
              <td>数据A</td>
              <td>数据B</td>
              <td>数据C</td>
            </tr>
          </tbody>
        </table>
        <p>预期效果：清晰的边框+表头背景色+合适的内边距</p>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'improved-table-test.docx');
    await wordExportService.exportHtmlToWordFile(tableHtml, outputPath, {
      title: '改进的表格测试',
      author: '测试系统'
    });
    console.log('✅ 改进的表格处理测试通过');
    console.log('   预期效果：2px外边框+1px内边框+表头灰色背景+8px内边距');
    return true;
  } catch (error) {
    console.log('❌ 改进的表格处理测试失败:', error.message);
    return false;
  }
}

async function testCombinedFeatures() {
  console.log('\n=== 测试组合功能 ===');
  
  const wordExportService = new WordExportService();
  
  const combinedHtml = `
    <html>
      <body>
        <h1>组合功能测试</h1>
        <p>这是一个包含<span style="text-emphasis: filled currentColor;" data-emphasis-mark="dot">着重号</span>的段落。</p>
        
        <table style="width: 100%;">
          <tr>
            <th>项目</th>
            <th>描述</th>
          </tr>
          <tr>
            <td>着重号</td>
            <td>使用<span data-emphasis-mark="dot" style="text-emphasis: filled currentColor;">粗体+背景色+下划线</span>表示</td>
          </tr>
          <tr>
            <td>表格</td>
            <td>使用改进的边框和样式</td>
          </tr>
        </table>
        
        <p>测试完成！</p>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'combined-features-test.docx');
    await wordExportService.exportHtmlToWordFile(combinedHtml, outputPath, {
      title: '组合功能测试',
      author: '测试系统'
    });
    console.log('✅ 组合功能测试通过');
    return true;
  } catch (error) {
    console.log('❌ 组合功能测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('开始改进功能测试...');
  
  const tests = [
    testImprovedEmphasisMarks,
    testImprovedTable,
    testCombinedFeatures
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    const result = await test();
    if (result) {
      passedTests++;
    }
  }
  
  console.log('\n=== 改进功能测试总结 ===');
  console.log(`总测试数: ${tests.length}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${tests.length - passedTests}`);
  
  if (passedTests === tests.length) {
    console.log('🎉 所有改进功能测试通过！');
    console.log('\n📋 改进效果总结：');
    console.log('1. 着重号：粗体+黄色背景+下划线（更明显的视觉效果）');
    console.log('2. 表格：2px外边框+表头背景色+更好的内边距（更清晰的结构）');
    console.log('3. 组合使用：两种改进可以很好地配合使用');
  } else {
    console.log('⚠️  部分改进功能测试失败，请检查相关功能。');
  }
  
  console.log('\n📁 生成的测试文件：');
  console.log('- improved-emphasis-test.docx - 着重号改进测试');
  console.log('- improved-table-test.docx - 表格改进测试');
  console.log('- combined-features-test.docx - 组合功能测试');
  console.log('\n请手动打开这些文件验证改进效果！');
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

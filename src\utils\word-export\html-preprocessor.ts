/**
 * HTML预处理工具
 * 处理html-to-docx无法很好处理的HTML元素和样式
 */
import { JSDOM } from 'jsdom';

export interface PreprocessOptions {
  /** 是否处理着重号 */
  processEmphasisMarks?: boolean;
  /** 是否处理display:inline-block元素 */
  processInlineBlocks?: boolean;
  /** 是否处理浮动元素 */
  processFloatingElements?: boolean;
  /** 是否优化表格样式 */
  optimizeTableStyles?: boolean;
}

/**
 * HTML预处理器
 */
export class HtmlPreprocessor {
  private dom: JSDOM;
  private document: Document;

  constructor(htmlContent: string) {
    this.dom = new JSDOM(htmlContent);
    this.document = this.dom.window.document;
  }

  /**
   * 执行预处理
   */
  preprocess(options: PreprocessOptions = {}): string {
    const {
      processEmphasisMarks = true,
      processInlineBlocks = true,
      processFloatingElements = true,
      optimizeTableStyles = true,
    } = options;

    console.log('开始HTML预处理...');

    if (processEmphasisMarks) {
      this.processEmphasisMarks();
    }

    if (processInlineBlocks) {
      this.processInlineBlockElements();
    }

    if (processFloatingElements) {
      this.removeFloatingElements();
    }

    if (optimizeTableStyles) {
      this.optimizeTableStyles();
    }

    // 清理可能导致问题的样式
    this.cleanupProblematicStyles();

    console.log('HTML预处理完成');
    return this.getProcessedHtml();
  }

  /**
   * 处理着重号
   * 将CSS着重号样式转换为更兼容的格式
   */
  private processEmphasisMarks(): void {
    console.log('处理着重号...');

    // 查找所有带有着重号样式的元素
    const emphasisElements = this.document.querySelectorAll(
      '[data-emphasis-mark="dot"]'
    );

    emphasisElements.forEach(element => {
      const htmlElement = element as HTMLElement;

      // 检查是否有text-emphasis样式或data-emphasis-mark属性
      const style = htmlElement.style;
      const hasEmphasisStyle = style.textEmphasis ||
                              style.getPropertyValue('text-emphasis') ||
                              htmlElement.hasAttribute('data-emphasis-mark');

      if (hasEmphasisStyle) {
        // 移除CSS着重号样式
        style.removeProperty('text-emphasis');
        style.removeProperty('text-emphasis-position');

        // 使用更兼容的方式：添加背景色和粗体来模拟着重号效果
        style.fontWeight = 'bold';
        style.backgroundColor = '#ffff99'; // 浅黄色背景
        style.textDecoration = 'underline';
        style.border = '1px dotted #ff0000'; // 红色虚线边框

        // 添加一个特殊的类名，便于在Word中识别
        htmlElement.classList.add('emphasis-mark');

        console.log('转换着重号为粗体+背景色+下划线+边框样式');
      }
    });
  }

  /**
   * 处理display:inline-block元素
   * 确保这些元素在Word中正确显示
   */
  private processInlineBlockElements(): void {
    console.log('处理inline-block元素...');

    const inlineBlockElements = this.document.querySelectorAll('*');

    inlineBlockElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      if (style.display === 'inline-block') {
        // 对于inline-block元素，确保它们在Word中正确显示
        // 可以考虑转换为inline或block，取决于上下文
        const parent = htmlElement.parentElement;

        if (parent && parent.style.whiteSpace === 'nowrap') {
          // 如果父元素有nowrap，保持inline
          style.display = 'inline';
        }
      }
    });
  }

  /**
   * 移除浮动元素
   * position:absolute等浮动元素在Word转换中可能造成问题
   */
  private removeFloatingElements(): void {
    console.log('处理浮动元素...');

    const floatingElements = this.document.querySelectorAll('*');

    floatingElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      if (style.position === 'absolute' || style.position === 'fixed') {
        // 移除浮动定位，避免在Word转换中造成布局问题
        style.removeProperty('position');
        style.removeProperty('top');
        style.removeProperty('left');
        style.removeProperty('right');
        style.removeProperty('bottom');

        console.log('移除浮动定位样式');
      }
    });
  }

  /**
   * 优化表格样式
   * 确保表格在Word中正确显示
   */
  private optimizeTableStyles(): void {
    console.log('优化表格样式...');

    const tables = this.document.querySelectorAll('table');

    tables.forEach(table => {
      const htmlTable = table as HTMLTableElement;

      // 重建表格，移除所有可能有问题的属性
      this.rebuildTable(htmlTable);
    });
  }

  /**
   * 重建表格，移除所有可能导致问题的属性
   */
  private rebuildTable(table: HTMLTableElement): void {
    console.log('重建表格...');

    // 创建新的表格元素
    const newTable = this.document.createElement('table');
    newTable.style.border = '2px solid #333';
    newTable.style.width = '100%';
    newTable.style.borderCollapse = 'collapse';
    newTable.style.marginTop = '10px';
    newTable.style.marginBottom = '10px';

    // 检查是否有tbody，如果没有则查找直接的tr
    let rows = table.querySelectorAll('tbody tr');
    if (rows.length === 0) {
      rows = table.querySelectorAll('tr');
    }

    rows.forEach((row, rowIndex) => {
      const newRow = this.document.createElement('tr');

      // 获取所有单元格
      const cells = row.querySelectorAll('td, th');

      cells.forEach((cell) => {
        const isHeader = cell.tagName.toLowerCase() === 'th' || rowIndex === 0;
        const newCell = this.document.createElement(isHeader ? 'th' : 'td');

        // 复制文本内容
        newCell.innerHTML = cell.innerHTML;

        // 设置基本样式
        newCell.style.border = '1px solid #666';
        newCell.style.padding = '8px';
        newCell.style.verticalAlign = 'top';

        // 表头样式
        if (isHeader) {
          newCell.style.backgroundColor = '#f0f0f0';
          newCell.style.fontWeight = 'bold';
          newCell.style.textAlign = 'center';
        }

        // 复制一些安全的样式
        const originalStyle = (cell as HTMLElement).style;
        if (originalStyle.textAlign && !isHeader) {
          newCell.style.textAlign = originalStyle.textAlign;
        }
        if (originalStyle.fontWeight && !isHeader) {
          newCell.style.fontWeight = originalStyle.fontWeight;
        }

        newRow.appendChild(newCell);
      });

      newTable.appendChild(newRow);
    });

    // 替换原表格
    table.parentNode?.replaceChild(newTable, table);
  }

  /**
   * 清理可能导致问题的样式
   */
  private cleanupProblematicStyles(): void {
    console.log('清理问题样式...');

    const allElements = this.document.querySelectorAll('*');

    allElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      // 清理可能导致XML问题的样式属性
      const problematicProperties = [
        'white-space-collapse',
        'overflow-wrap',
        'word-break',
        'max-width',
      ];

      problematicProperties.forEach(prop => {
        if (style.getPropertyValue(prop)) {
          style.removeProperty(prop);
        }
      });

      // 清理可能有问题的类名
      if (htmlElement.className) {
        // 移除可能导致问题的类
        const problematicClasses = ['MsoNormal', 'double-underline'];
        problematicClasses.forEach(className => {
          htmlElement.classList.remove(className);
        });
      }

      // 清理align属性（使用style代替）
      if (htmlElement.hasAttribute('align')) {
        const alignValue = htmlElement.getAttribute('align');
        htmlElement.removeAttribute('align');
        if (alignValue && !style.textAlign) {
          style.textAlign = alignValue;
        }
      }

      // 特别处理表格单元格的width属性
      if (htmlElement.tagName === 'TD' || htmlElement.tagName === 'TH') {
        // 移除width属性，这可能导致XML问题
        htmlElement.removeAttribute('width');

        // 清理可能有问题的样式值
        if (style.width && style.width.includes('px')) {
          // 将像素值转换为百分比或移除
          style.removeProperty('width');
        }
      }
    });
  }

  /**
   * 获取处理后的HTML
   */
  private getProcessedHtml(): string {
    return this.dom.serialize();
  }
}

/**
 * 预处理HTML内容
 * @param htmlContent 原始HTML内容
 * @param options 预处理选项
 * @returns 预处理后的HTML内容
 */
export function preprocessHtml(
  htmlContent: string,
  options: PreprocessOptions = {}
): string {
  const preprocessor = new HtmlPreprocessor(htmlContent);
  return preprocessor.preprocess(options);
}

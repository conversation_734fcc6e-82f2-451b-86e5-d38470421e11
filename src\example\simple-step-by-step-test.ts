/**
 * 简单分步测试
 * 1. 使用特殊标记标识着重号
 * 2. html-to-docx快速转换（预期：着重号外其他样式正常）
 * 3. docx库处理特殊标记（预期：着重号也正常）
 */
import * as path from 'path';
import * as fs from 'fs';
import * as HTMLtoDOCX from 'html-to-docx';

async function step1_CreateSimpleHtml() {
  console.log('\n=== 步骤1：创建简单HTML测试片段 ===');
  
  // 使用特殊标记 [EMPHASIS]...[/EMPHASIS] 来标识着重号
  const simpleHtml = `
    <html>
      <head>
        <meta charset="UTF-8">
        <title>简单着重号测试</title>
      </head>
      <body>
        <h1>简单着重号测试</h1>
        
        <h2>一、基础测试</h2>
        <p>这是普通段落，没有着重号。</p>
        <p>这是包含<strong>[EMPHASIS]着重号文字[/EMPHASIS]</strong>的段落。</p>
        <p>这是另一个包含<strong>[EMPHASIS]更多着重号[/EMPHASIS]</strong>的段落。</p>
        
        <h2>二、表格测试</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th style="background-color: #f0f0f0; padding: 8px;">题型</th>
            <th style="background-color: #f0f0f0; padding: 8px;">内容</th>
          </tr>
          <tr>
            <td style="padding: 8px;">选择题</td>
            <td style="padding: 8px;">包含<strong>[EMPHASIS]表格着重号[/EMPHASIS]</strong>的内容</td>
          </tr>
          <tr>
            <td style="padding: 8px;">填空题</td>
            <td style="padding: 8px;">普通表格内容</td>
          </tr>
        </table>
        
        <h2>三、列表测试</h2>
        <ul>
          <li>列表项1：<strong>[EMPHASIS]列表着重号[/EMPHASIS]</strong></li>
          <li>列表项2：普通列表内容</li>
        </ul>
        
        <h2>四、格式测试</h2>
        <p style="text-align: center; color: blue;">
          居中蓝色段落，包含<strong>[EMPHASIS]居中着重号[/EMPHASIS]</strong>。
        </p>
        <p style="text-align: right; font-size: 18px;">
          右对齐大字段落，包含<strong>[EMPHASIS]右对齐着重号[/EMPHASIS]</strong>。
        </p>
      </body>
    </html>
  `;
  
  // 保存HTML文件
  const htmlPath = path.join(process.cwd(), 'testFiles', 'simple-step-test.html');
  await fs.promises.writeFile(htmlPath, simpleHtml, 'utf-8');
  
  console.log(`✅ HTML文件已保存: ${htmlPath}`);
  console.log('特殊标记说明：');
  console.log('- [EMPHASIS]...[/EMPHASIS] 标识需要着重号的文字');
  console.log('- 其他格式：表格、列表、段落对齐、颜色等');
  
  return { htmlContent: simpleHtml, htmlPath };
}

async function step2_HtmlToDocxConversion(htmlContent: string) {
  console.log('\n=== 步骤2：html-to-docx快速转换 ===');
  
  try {
    // 使用html-to-docx进行基础转换
    const docxOptions = {
      table: { row: { cantSplit: true } },
      footer: true,
      pageNumber: true,
      margins: {
        top: 1440,
        right: 1440,
        bottom: 1440,
        left: 1440,
      },
      orientation: 'portrait' as const,
      title: '简单着重号测试',
      creator: '测试系统',
    };

    const docxBuffer = await HTMLtoDOCX(htmlContent, null, docxOptions);
    
    // 保存第一步转换结果
    const step2Path = path.join(process.cwd(), 'testFiles', 'step2-html-to-docx-result.docx');
    await fs.promises.writeFile(step2Path, docxBuffer);
    
    console.log(`✅ html-to-docx转换完成: ${step2Path}`);
    console.log('预期效果：');
    console.log('- ✅ 表格格式正常');
    console.log('- ✅ 列表结构正常');
    console.log('- ✅ 段落对齐和颜色正常');
    console.log('- ❓ 着重号显示为 [EMPHASIS]文字[/EMPHASIS]（待处理）');
    
    return { docxBuffer, step2Path };
  } catch (error) {
    console.error('❌ html-to-docx转换失败:', error);
    throw error;
  }
}

async function step3_DocxLibraryProcessing(docxBuffer: Buffer, originalHtml: string) {
  console.log('\n=== 步骤3：docx库处理特殊标记 ===');
  
  try {
    // 导入docx库处理模块
    const JSZip = require('jszip');
    const { DOMParser, XMLSerializer } = require('xmldom');
    
    // 解析Word文档
    const zip = new JSZip();
    const docxZip = await zip.loadAsync(docxBuffer);
    
    // 读取document.xml
    const documentXml = await docxZip.file('word/document.xml')?.async('text');
    if (!documentXml) {
      throw new Error('无法读取Word文档内容');
    }

    console.log('✅ 成功读取Word文档XML');

    // 解析XML
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(documentXml, 'text/xml');

    // 查找所有文本节点，处理特殊标记
    const textNodes = xmlDoc.getElementsByTagName('w:t');
    let processedCount = 0;
    
    for (let i = 0; i < textNodes.length; i++) {
      const textNode = textNodes[i];
      const textContent = textNode.textContent || '';
      
      // 检查是否包含特殊标记
      if (textContent.includes('[EMPHASIS]') && textContent.includes('[/EMPHASIS]')) {
        console.log(`找到特殊标记文本: "${textContent}"`);
        
        // 处理特殊标记
        const processed = processEmphasisMarks(textNode, xmlDoc);
        if (processed) {
          processedCount++;
        }
      }
    }

    console.log(`✅ 处理了 ${processedCount} 个特殊标记`);

    // 序列化修改后的XML
    const serializer = new XMLSerializer();
    const modifiedXml = serializer.serializeToString(xmlDoc);

    // 更新ZIP中的document.xml
    docxZip.file('word/document.xml', modifiedXml);

    // 生成最终的Word文档
    const finalBuffer = await docxZip.generateAsync({ type: 'nodebuffer' });
    
    // 保存最终结果
    const finalPath = path.join(process.cwd(), 'testFiles', 'step3-final-result.docx');
    await fs.promises.writeFile(finalPath, finalBuffer);
    
    console.log(`✅ 最终文档已保存: ${finalPath}`);
    console.log('预期效果：');
    console.log('- ✅ 所有格式保持完整（表格、列表、对齐、颜色）');
    console.log('- ✅ 着重号正确显示（粗体 + 虚线下划线 + 高亮）');
    
    return { finalBuffer, finalPath };
  } catch (error) {
    console.error('❌ docx库处理失败:', error);
    throw error;
  }
}

/**
 * 处理文本节点中的着重号标记
 */
function processEmphasisMarks(textNode: any, xmlDoc: any): boolean {
  try {
    const textContent = textNode.textContent || '';
    
    // 使用正则表达式匹配着重号标记
    const emphasisRegex = /\[EMPHASIS\](.*?)\[\/EMPHASIS\]/g;
    
    if (!emphasisRegex.test(textContent)) {
      return false;
    }
    
    // 重置正则表达式
    emphasisRegex.lastIndex = 0;
    
    // 找到包含此文本节点的运行(w:r)
    const runNode = findParentRun(textNode);
    if (!runNode) {
      return false;
    }
    
    // 分割文本，创建新的运行节点
    const parts = textContent.split(emphasisRegex);
    const matches = [...textContent.matchAll(/\[EMPHASIS\](.*?)\[\/EMPHASIS\]/g)];
    
    if (matches.length === 0) {
      return false;
    }
    
    // 清空原始文本节点
    textNode.textContent = '';
    
    // 获取父段落
    const paragraphNode = findParentParagraph(runNode);
    if (!paragraphNode) {
      return false;
    }
    
    // 创建新的运行节点
    let partIndex = 0;
    for (let i = 0; i < parts.length; i++) {
      if (parts[i]) {
        // 普通文本
        const normalRun = createTextRun(xmlDoc, parts[i], false);
        paragraphNode.insertBefore(normalRun, runNode);
      }
      
      // 着重号文本
      if (partIndex < matches.length) {
        const emphasisText = matches[partIndex][1];
        if (emphasisText) {
          const emphasisRun = createTextRun(xmlDoc, emphasisText, true);
          paragraphNode.insertBefore(emphasisRun, runNode);
        }
        partIndex++;
      }
    }
    
    // 移除原始运行节点
    paragraphNode.removeChild(runNode);
    
    console.log(`处理着重号标记: "${textContent}"`);
    return true;
  } catch (error) {
    console.error('处理着重号标记失败:', error);
    return false;
  }
}

/**
 * 创建文本运行节点
 */
function createTextRun(xmlDoc: any, text: string, isEmphasis: boolean): any {
  const runNode = xmlDoc.createElement('w:r');
  
  // 创建运行属性
  const rPrNode = xmlDoc.createElement('w:rPr');
  
  if (isEmphasis) {
    // 添加粗体
    const boldNode = xmlDoc.createElement('w:b');
    rPrNode.appendChild(boldNode);
    
    // 添加下划线
    const underlineNode = xmlDoc.createElement('w:u');
    underlineNode.setAttribute('w:val', 'dotted');
    rPrNode.appendChild(underlineNode);
    
    // 添加高亮
    const highlightNode = xmlDoc.createElement('w:highlight');
    highlightNode.setAttribute('w:val', 'yellow');
    rPrNode.appendChild(highlightNode);
  }
  
  runNode.appendChild(rPrNode);
  
  // 创建文本节点
  const textNode = xmlDoc.createElement('w:t');
  textNode.textContent = text;
  runNode.appendChild(textNode);
  
  return runNode;
}

/**
 * 查找文本节点的父运行节点
 */
function findParentRun(textNode: any): any {
  let parent = textNode.parentNode;
  while (parent) {
    if (parent.nodeName === 'w:r') {
      return parent;
    }
    parent = parent.parentNode;
  }
  return null;
}

/**
 * 查找运行节点的父段落节点
 */
function findParentParagraph(runNode: any): any {
  let parent = runNode.parentNode;
  while (parent) {
    if (parent.nodeName === 'w:p') {
      return parent;
    }
    parent = parent.parentNode;
  }
  return null;
}

async function main() {
  console.log('开始简单分步测试...');
  console.log('目标：验证 html-to-docx + docx库 的组合处理效果');
  
  try {
    // 步骤1：创建简单HTML
    const { htmlContent } = await step1_CreateSimpleHtml();
    
    // 步骤2：html-to-docx转换
    const { docxBuffer } = await step2_HtmlToDocxConversion(htmlContent);
    
    // 步骤3：docx库处理特殊标记
    await step3_DocxLibraryProcessing(docxBuffer, htmlContent);
    
    console.log('\n🎉 简单分步测试完成！');
    console.log('\n📁 生成的文件：');
    console.log('- simple-step-test.html - 原始HTML文件');
    console.log('- step2-html-to-docx-result.docx - html-to-docx转换结果');
    console.log('- step3-final-result.docx - 最终处理结果');
    console.log('\n请手动打开Word文档验证效果：');
    console.log('1. step2文档：应该看到表格、列表等格式正常，着重号显示为[EMPHASIS]文字[/EMPHASIS]');
    console.log('2. step3文档：应该看到所有格式正常，着重号显示为粗体+虚线下划线+高亮');
    
  } catch (error) {
    console.error('❌ 简单分步测试失败:', error);
  }
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

/**
 * 复杂HTML文档使用docx库处理着重号的测试
 */
import * as fs from 'fs';
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function main() {
  try {
    console.log('开始复杂HTML文档的docx库着重号测试...');

    const wordExportService = new WordExportService();

    // 读取原始的复杂HTML文件
    const htmlFilePath = path.join(process.cwd(), 'testFiles', 'input-html-sample.html');
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf-8');
    console.log(`已读取HTML文件: ${htmlFilePath}`);

    // 设置输出路径
    const outputPath = path.join(process.cwd(), 'testFiles', 'complex-docx-emphasis-result.docx');

    // 使用docx库处理着重号
    console.log('开始使用docx库转换复杂HTML到Word...');
    const savedPath = await wordExportService.exportHtmlToWordFile(
      htmlContent,
      outputPath,
      {
        title: '复杂文档docx库着重号测试',
        author: '测试系统',
        orientation: 'portrait',
        useDocxForEmphasis: true, // 启用docx库处理着重号
        emphasisOptions: {
          emphasisStyle: 'dot',
          useUnderlineAsEmphasis: true,
          useBoldForEmphasis: true,
        }
      }
    );

    console.log(`✅ 复杂HTML文档的docx库着重号测试完成！`);
    console.log(`Word文档保存成功: ${savedPath}`);
    console.log('\n📋 预期效果：');
    console.log('- 着重号文字：下划线+粗体+黄色高亮');
    console.log('- 表格：简单的单线边框');
    console.log('- 其他格式：保持原有样式');
    console.log('\n请手动打开Word文档验证着重号效果！');

  } catch (error) {
    console.error('复杂HTML文档的docx库着重号测试失败:', error);
  }
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

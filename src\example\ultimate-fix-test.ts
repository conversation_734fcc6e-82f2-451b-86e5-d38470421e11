/**
 * 终极修复测试
 * 彻底解决着重号显示异常和表格双线边框问题
 */
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function testUltimateFix() {
  console.log('\n=== 终极修复测试：着重号 + 表格单线边框 ===');
  
  const wordExportService = new WordExportService();
  
  const testHtml = `
    <html>
      <body>
        <h1>终极修复测试</h1>
        
        <h2>一、着重号测试（改进方案）</h2>
        <p><strong>1. 下面对有新疆房的语言的理解，错误的一项是（　　）</strong></p>
        <p>A. A. 边疆（<span data-emphasis-mark="dot">重返国界的领土</span>）</p>
        <p>B. B. 绚丽多彩（<span data-emphasis-mark="dot">各种各样</span>的色彩加以美丽）</p>
        <p>C. C. 坪坝（<span data-emphasis-mark="dot">高高低低</span>的坝地）</p>
        <p>D. D. 高高低低工厂对有新疆房的可了的</p>
        
        <h2>二、表格测试（彻底单线边框）</h2>
        <table border="1" style="width: 100%;">
          <tr>
            <th>题型</th>
            <th>内容</th>
            <th>着重号测试</th>
          </tr>
          <tr>
            <td>选择题</td>
            <td>普通文字内容</td>
            <td><span data-emphasis-mark="dot">着重号文字</span></td>
          </tr>
          <tr>
            <td>填空题</td>
            <td>更多测试内容</td>
            <td>无着重号</td>
          </tr>
          <tr>
            <td>判断题</td>
            <td><span data-emphasis-mark="dot">表格内着重号</span></td>
            <td>混合内容</td>
          </tr>
        </table>
        
        <h2>三、复杂表格测试（无双线）</h2>
        <table style="width: 100%; border-collapse: collapse;" border="1">
          <thead>
            <tr style="background-color: #f0f0f0;">
              <th style="border: 1px solid black; padding: 8px;">序号</th>
              <th style="border: 1px solid black; padding: 8px;">题目内容</th>
              <th style="border: 1px solid black; padding: 8px;">选项</th>
              <th style="border: 1px solid black; padding: 8px;">答案</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="border: 1px solid black; padding: 8px;">1</td>
              <td style="border: 1px solid black; padding: 8px;">这是包含<span data-emphasis-mark="dot">着重号</span>的题目</td>
              <td style="border: 1px solid black; padding: 8px;">A. 选项一<br/>B. 选项二</td>
              <td style="border: 1px solid black; padding: 8px;">A</td>
            </tr>
            <tr>
              <td style="border: 1px solid black; padding: 8px;">2</td>
              <td style="border: 1px solid black; padding: 8px;">普通题目内容测试</td>
              <td style="border: 1px solid black; padding: 8px;">A. <span data-emphasis-mark="dot">着重选项</span><br/>B. 普通选项</td>
              <td style="border: 1px solid black; padding: 8px;">B</td>
            </tr>
            <tr>
              <td style="border: 1px solid black; padding: 8px;">3</td>
              <td style="border: 1px solid black; padding: 8px;">长文本内容测试，包含多个<span data-emphasis-mark="dot">着重号词汇</span>和普通文字</td>
              <td style="border: 1px solid black; padding: 8px;">A. 选项<br/>B. 选项<br/>C. 选项</td>
              <td style="border: 1px solid black; padding: 8px;">C</td>
            </tr>
          </tbody>
        </table>
        
        <h2>四、嵌套表格测试</h2>
        <table style="width: 100%;" border="1">
          <tr>
            <td style="width: 50%;">
              <p>左侧内容：<span data-emphasis-mark="dot">着重号测试</span></p>
              <table style="width: 100%;" border="1">
                <tr>
                  <td>嵌套表格1</td>
                  <td><span data-emphasis-mark="dot">嵌套着重号</span></td>
                </tr>
                <tr>
                  <td>嵌套表格2</td>
                  <td>普通内容</td>
                </tr>
              </table>
            </td>
            <td style="width: 50%;">
              <p>右侧内容：普通文字</p>
              <ul>
                <li>列表项1：<span data-emphasis-mark="dot">着重号</span></li>
                <li>列表项2：普通内容</li>
              </ul>
            </td>
          </tr>
        </table>
        
        <h2>五、混合格式测试</h2>
        <p style="text-align: center; color: blue;">
          居中蓝色段落，包含<span data-emphasis-mark="dot">着重号文字</span>，测试格式保持。
        </p>
        
        <div style="border: 2px solid red; padding: 10px; margin: 10px;">
          <h3>边框容器内的内容</h3>
          <p>包含<span data-emphasis-mark="dot">着重号</span>的段落内容。</p>
          <table style="width: 100%;" border="1">
            <tr>
              <td>容器内表格</td>
              <td><span data-emphasis-mark="dot">容器内着重号</span></td>
            </tr>
          </table>
        </div>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'ultimate-fix-test.docx');
    
    // 使用修复后的方案
    await wordExportService.exportHtmlToWordFile(testHtml, outputPath, {
      title: '终极修复测试',
      author: '测试系统',
      postprocessOptions: {
        processEmphasisMarks: false, // 使用预处理中的改进方案
      }
    });
    
    console.log('✅ 终极修复测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('\n🎯 修复内容：');
    console.log('1. 着重号：改进Unicode处理 + CSS备用方案');
    console.log('2. 表格：彻底清理边框属性，重新设置单线边框');
    console.log('3. 兼容性：支持嵌套表格和复杂布局');
    console.log('\n📋 预期效果：');
    console.log('- 着重号：正确显示，无异常字符');
    console.log('- 表格：完全单线边框，无双线问题');
    console.log('- 格式：所有样式完整保持');
    return true;
  } catch (error) {
    console.log('❌ 终极修复测试失败:', error.message);
    return false;
  }
}

async function testComplexDocumentUltimateFix() {
  console.log('\n=== 复杂文档终极修复测试 ===');
  
  const wordExportService = new WordExportService();
  
  try {
    // 读取原始的复杂HTML文件
    const fs = require('fs');
    const htmlFilePath = path.join(process.cwd(), 'testFiles', 'input-html-sample.html');
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf-8');
    console.log(`已读取HTML文件: ${htmlFilePath}`);

    const outputPath = path.join(process.cwd(), 'testFiles', 'complex-ultimate-fix.docx');
    
    // 使用终极修复方案
    await wordExportService.exportHtmlToWordFile(htmlContent, outputPath, {
      title: '复杂文档终极修复',
      author: '测试系统',
      postprocessOptions: {
        processEmphasisMarks: false, // 使用改进的着重号方案
      }
    });
    
    console.log('✅ 复杂文档终极修复测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：');
    console.log('- 着重号显示正常，无异常字符');
    console.log('- 所有表格完全单线边框');
    console.log('- 所有其他格式完整保持');
    return true;
  } catch (error) {
    console.log('❌ 复杂文档终极修复测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('开始终极修复测试...');
  
  const tests = [
    testUltimateFix,
    testComplexDocumentUltimateFix,
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    const result = await test();
    if (result) {
      passedTests++;
    }
  }
  
  console.log('\n=== 终极修复测试总结 ===');
  console.log(`总测试数: ${tests.length}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${tests.length - passedTests}`);
  
  if (passedTests === tests.length) {
    console.log('🎉 终极修复测试通过！');
    console.log('\n🔧 终极修复方案：');
    console.log('1. 着重号：改进Unicode处理，添加CSS备用方案');
    console.log('2. 表格：彻底清理所有边框属性，重新设置单线边框');
    console.log('3. 兼容性：支持复杂嵌套结构和各种格式');
    console.log('\n这是彻底解决问题的终极方案！');
  } else {
    console.log('⚠️  部分终极修复测试失败，请检查相关功能。');
  }
  
  console.log('\n📁 生成的测试文件：');
  console.log('- ultimate-fix-test.docx - 终极修复完整测试');
  console.log('- complex-ultimate-fix.docx - 复杂文档终极修复');
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

# 着重号处理方案总结

## 问题背景

在HTML转Word的过程中，CSS着重号（`text-emphasis`）无法被html-to-docx库正确处理，导致着重号在生成的Word文档中不显示。

## 解决方案

我们提供了两种着重号处理方案：

### 方案一：标准预处理方案（html-to-docx）

**实现方式**：
- 在HTML预处理阶段将着重号转换为html-to-docx支持的样式
- 使用粗体+黄色背景+下划线+红色虚线边框的组合样式

**优点**：
- 兼容性好，使用现有的html-to-docx转换流程
- 处理速度快
- 支持复杂的HTML结构（表格、图片等）

**缺点**：
- 着重号效果不够理想，只是视觉上的模拟
- 依赖html-to-docx的样式支持

**使用方法**：
```typescript
await wordExportService.exportHtmlToWordFile(htmlContent, outputPath, {
  useDocxForEmphasis: false, // 使用标准方案
});
```

### 方案二：docx库专业处理方案

**实现方式**：
- 跳过HTML预处理中的着重号处理
- 使用docx库直接解析HTML并生成Word文档
- 对着重号元素应用专业的Word格式

**优点**：
- 着重号效果更专业（下划线+粗体+黄色高亮）
- 使用Word原生格式，效果更接近真正的着重号
- 可以精确控制文本格式

**缺点**：
- 只处理文本内容，复杂的HTML结构（表格、图片）支持有限
- 处理逻辑相对复杂

**使用方法**：
```typescript
await wordExportService.exportHtmlToWordFile(htmlContent, outputPath, {
  useDocxForEmphasis: true, // 使用docx库方案
  emphasisOptions: {
    emphasisStyle: 'dot',
    useUnderlineAsEmphasis: true,
    useBoldForEmphasis: true,
  }
});
```

## 技术实现

### 着重号检测

两种方案都支持以下着重号标记：
- `data-emphasis-mark="dot"` 属性
- `text-emphasis: filled currentColor` CSS样式
- `.emphasis-mark` 类名

### 样式转换

**标准方案转换**：
```css
/* 原始着重号样式 */
text-emphasis: filled currentColor;
text-emphasis-position: under right;

/* 转换后的样式 */
font-weight: bold;
background-color: #ffff99;
text-decoration: underline;
border: 1px dotted #ff0000;
```

**docx库方案转换**：
```typescript
new TextRun({
  text: '着重号文字',
  bold: true,
  underline: {
    type: UnderlineType.DOTTED,
    color: '000000',
  },
  highlight: 'yellow',
});
```

## 测试结果

### 测试文件

生成了以下测试文件用于对比：

1. **标准方案测试**：
   - `standard-emphasis-test.docx` - 标准方案处理结果
   - `output-word-result.docx` - 完整试题标准处理结果

2. **docx库方案测试**：
   - `docx-emphasis-test.docx` - docx库方案处理结果
   - `complex-docx-emphasis-result.docx` - 完整试题docx库处理结果

### 效果对比

| 方案 | 着重号效果 | 表格支持 | 图片支持 | 复杂布局 | 处理速度 |
|------|------------|----------|----------|----------|----------|
| 标准方案 | 粗体+背景色+下划线 | ✅ 完整支持 | ✅ 完整支持 | ✅ 完整支持 | ⚡ 快 |
| docx库方案 | 下划线+粗体+高亮 | ❌ 基础支持 | ❌ 不支持 | ❌ 基础支持 | 🐌 较慢 |

## 使用建议

### 推荐使用场景

**标准方案适用于**：
- 包含复杂表格、图片的文档
- 需要保持完整HTML结构的场景
- 对处理速度有要求的场景
- 着重号数量较少的文档

**docx库方案适用于**：
- 主要包含文本内容的文档
- 对着重号效果要求较高的场景
- 着重号数量较多的文档
- 可以接受简化其他格式的场景

### 混合使用策略

可以根据文档内容动态选择方案：

```typescript
// 检测文档复杂度
const hasComplexStructure = htmlContent.includes('<table>') || 
                           htmlContent.includes('<img>') ||
                           htmlContent.includes('display:');

// 检测着重号数量
const emphasisCount = (htmlContent.match(/data-emphasis-mark/g) || []).length;

// 选择合适的方案
const useDocxForEmphasis = !hasComplexStructure && emphasisCount > 5;

await wordExportService.exportHtmlToWordFile(htmlContent, outputPath, {
  useDocxForEmphasis,
  // ... 其他选项
});
```

## 总结

通过提供两种着重号处理方案，我们成功解决了HTML转Word过程中着重号不显示的问题：

1. **标准方案**：适合大多数场景，保持完整的HTML转换能力
2. **docx库方案**：专门优化着重号效果，适合文本为主的文档

用户可以根据具体需求选择合适的方案，或者结合文档特点动态选择，实现最佳的转换效果。

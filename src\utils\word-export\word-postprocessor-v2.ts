/**
 * Word文档二次处理器 V2
 * 规范的两步处理流程：
 * 1. html-to-docx 快速处理大部分内容
 * 2. docx库二次处理特殊样式（着重号等）
 */
import { Document, Packer, Paragraph, TextRun, UnderlineType } from 'docx';
import { JSDOM } from 'jsdom';
import * as JSZip from 'jszip';

export interface PostProcessorOptions {
  /** 是否处理着重号 */
  processEmphasisMarks?: boolean;
  /** 是否处理特殊字体 */
  processSpecialFonts?: boolean;
  /** 是否处理特殊颜色 */
  processSpecialColors?: boolean;
  /** 原始HTML内容，用于提取特殊样式信息 */
  originalHtmlContent?: string;
}

export interface EmphasisInfo {
  text: string;
  isEmphasis: boolean;
  startIndex: number;
  endIndex: number;
}

/**
 * Word文档二次处理器
 * 在html-to-docx基础上进行特殊样式的二次处理
 */
export class WordPostProcessorV2 {
  private docxBuffer: Buffer;
  private originalHtml: string;

  constructor(docxBuffer: Buffer, originalHtml: string = '') {
    this.docxBuffer = docxBuffer;
    this.originalHtml = originalHtml;
  }

  /**
   * 执行二次处理
   */
  async postProcess(options: PostProcessorOptions = {}): Promise<Buffer> {
    const {
      originalHtmlContent,
    } = options;

    console.log('开始Word文档二次处理...');

    // 检查是否需要处理特殊样式
    const needsProcessing = this.needsSpecialProcessing(options);
    
    if (!needsProcessing) {
      console.log('无需特殊处理，返回原始文档');
      return this.docxBuffer;
    }

    try {
      // 提取html-to-docx生成的基础内容
      const baseContent = await this.extractBaseContent();
      console.log(`提取了 ${baseContent.paragraphs.length} 个段落的基础内容`);

      // 提取原始HTML中的特殊样式信息
      const styleInfo = this.extractSpecialStyles(originalHtmlContent || this.originalHtml, options);

      // 使用docx库重新生成文档，应用特殊样式
      const enhancedBuffer = await this.generateEnhancedDocument(baseContent, styleInfo, options);

      console.log('Word文档二次处理完成');
      return enhancedBuffer;

    } catch (error) {
      console.error('二次处理失败，回退到原始文档:', error);
      return this.docxBuffer;
    }
  }

  /**
   * 检查是否需要特殊处理
   */
  private needsSpecialProcessing(options: PostProcessorOptions): boolean {
    if (options.processEmphasisMarks && this.hasEmphasisMarks()) {
      console.log('检测到着重号，需要二次处理');
      return true;
    }

    if (options.processSpecialFonts && this.hasSpecialFonts()) {
      console.log('检测到特殊字体，需要二次处理');
      return true;
    }

    if (options.processSpecialColors && this.hasSpecialColors()) {
      console.log('检测到特殊颜色，需要二次处理');
      return true;
    }

    return false;
  }

  /**
   * 检查是否有着重号
   */
  private hasEmphasisMarks(): boolean {
    return this.originalHtml.includes('data-emphasis-mark') || 
           this.originalHtml.includes('text-emphasis');
  }

  /**
   * 检查是否有特殊字体（预留）
   */
  private hasSpecialFonts(): boolean {
    // TODO: 实现特殊字体检测
    return false;
  }

  /**
   * 检查是否有特殊颜色（预留）
   */
  private hasSpecialColors(): boolean {
    // TODO: 实现特殊颜色检测
    return false;
  }

  /**
   * 提取html-to-docx生成的基础内容
   */
  private async extractBaseContent(): Promise<{paragraphs: Array<{text: string, style?: any}>}> {
    try {
      // 解析Word文档
      const zip = new JSZip();
      const docxZip = await zip.loadAsync(this.docxBuffer);
      
      // 读取document.xml
      const documentXml = await docxZip.file('word/document.xml')?.async('text');
      if (!documentXml) {
        throw new Error('无法读取Word文档内容');
      }

      // 简化处理：直接从HTML提取文本内容
      const dom = new JSDOM(this.originalHtml);
      const document = dom.window.document;
      
      const paragraphs: Array<{text: string, style?: any}> = [];
      
      // 提取段落内容
      const htmlParagraphs = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6');
      htmlParagraphs.forEach(p => {
        const text = p.textContent?.trim();
        if (text) {
          paragraphs.push({
            text,
            style: this.extractElementStyle(p as HTMLElement)
          });
        }
      });

      return { paragraphs };
    } catch (error) {
      console.error('提取基础内容失败:', error);
      // 回退方案：从HTML直接提取
      return this.extractContentFromHtml();
    }
  }

  /**
   * 从HTML直接提取内容（回退方案）
   */
  private extractContentFromHtml(): {paragraphs: Array<{text: string, style?: any}>} {
    const dom = new JSDOM(this.originalHtml);
    const document = dom.window.document;
    
    const paragraphs: Array<{text: string, style?: any}> = [];
    
    const htmlParagraphs = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6');
    htmlParagraphs.forEach(p => {
      const text = p.textContent?.trim();
      if (text) {
        paragraphs.push({
          text,
          style: this.extractElementStyle(p as HTMLElement)
        });
      }
    });

    return { paragraphs };
  }

  /**
   * 提取元素样式信息
   */
  private extractElementStyle(element: HTMLElement): any {
    const style: any = {};
    
    // 提取基本样式
    if (element.style.fontWeight === 'bold' || element.tagName.match(/^H[1-6]$/)) {
      style.bold = true;
    }
    
    if (element.style.fontStyle === 'italic') {
      style.italic = true;
    }
    
    if (element.style.textAlign) {
      style.alignment = element.style.textAlign;
    }
    
    if (element.style.color) {
      style.color = element.style.color;
    }

    return style;
  }

  /**
   * 提取特殊样式信息
   */
  private extractSpecialStyles(htmlContent: string, options: PostProcessorOptions): any {
    const styleInfo: any = {};

    if (options.processEmphasisMarks) {
      styleInfo.emphasisMarks = this.extractEmphasisInfo(htmlContent);
    }

    // TODO: 添加其他特殊样式提取
    if (options.processSpecialFonts) {
      styleInfo.specialFonts = [];
    }

    if (options.processSpecialColors) {
      styleInfo.specialColors = [];
    }

    return styleInfo;
  }

  /**
   * 提取着重号信息
   */
  private extractEmphasisInfo(htmlContent: string): EmphasisInfo[] {
    const emphasisInfo: EmphasisInfo[] = [];
    
    try {
      const dom = new JSDOM(htmlContent);
      const document = dom.window.document;

      // 查找所有着重号元素
      const emphasisElements = document.querySelectorAll('[data-emphasis-mark], .emphasis-mark');
      
      emphasisElements.forEach(element => {
        const text = element.textContent?.trim();
        if (text) {
          emphasisInfo.push({
            text,
            isEmphasis: true,
            startIndex: 0, // TODO: 计算准确位置
            endIndex: text.length
          });
        }
      });

      console.log(`提取了 ${emphasisInfo.length} 个着重号`);
    } catch (error) {
      console.error('提取着重号信息失败:', error);
    }

    return emphasisInfo;
  }

  /**
   * 生成增强的Word文档
   */
  private async generateEnhancedDocument(
    baseContent: {paragraphs: Array<{text: string, style?: any}>},
    styleInfo: any,
    options: PostProcessorOptions
  ): Promise<Buffer> {
    const paragraphs: Paragraph[] = [];

    // 处理每个段落
    baseContent.paragraphs.forEach(paragraphData => {
      const runs: TextRun[] = [];
      
      if (options.processEmphasisMarks && styleInfo.emphasisMarks) {
        // 处理包含着重号的段落
        this.processEmphasisInParagraph(paragraphData.text, styleInfo.emphasisMarks, runs);
      } else {
        // 普通段落
        runs.push(new TextRun({
          text: paragraphData.text,
          bold: paragraphData.style?.bold,
          italics: paragraphData.style?.italic,
        }));
      }

      if (runs.length > 0) {
        paragraphs.push(new Paragraph({ children: runs }));
      }
    });

    // 创建Word文档
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: paragraphs,
        },
      ],
    });

    return await Packer.toBuffer(doc);
  }

  /**
   * 处理段落中的着重号
   */
  private processEmphasisInParagraph(
    paragraphText: string,
    emphasisMarks: EmphasisInfo[],
    runs: TextRun[]
  ): void {
    let hasEmphasis = false;
    
    // 检查段落是否包含着重号
    for (const emphasis of emphasisMarks) {
      if (paragraphText.includes(emphasis.text)) {
        hasEmphasis = true;
        break;
      }
    }

    if (hasEmphasis) {
      // 处理包含着重号的文本
      let remainingText = paragraphText;
      
      for (const emphasis of emphasisMarks) {
        if (remainingText.includes(emphasis.text)) {
          const parts = remainingText.split(emphasis.text);
          
          // 添加着重号前的文本
          if (parts[0]) {
            runs.push(new TextRun({ text: parts[0] }));
          }
          
          // 添加着重号文本
          runs.push(new TextRun({
            text: emphasis.text,
            bold: true,
            underline: {
              type: UnderlineType.DOTTED,
              color: '000000',
            },
            highlight: 'yellow',
          }));
          
          // 更新剩余文本
          remainingText = parts.slice(1).join(emphasis.text);
        }
      }
      
      // 添加剩余文本
      if (remainingText) {
        runs.push(new TextRun({ text: remainingText }));
      }
    } else {
      // 普通文本
      runs.push(new TextRun({ text: paragraphText }));
    }
  }
}

/**
 * 便捷函数：对Word文档进行二次处理
 */
export async function postProcessWordDocument(
  docxBuffer: Buffer,
  originalHtml: string,
  options: PostProcessorOptions = {}
): Promise<Buffer> {
  const processor = new WordPostProcessorV2(docxBuffer, originalHtml);
  return await processor.postProcess(options);
}

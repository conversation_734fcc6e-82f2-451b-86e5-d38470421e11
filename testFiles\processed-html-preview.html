<html><head></head><body>
        <h1>无着重号文档测试</h1>
        
        <h2>一、普通内容</h2>
        <p>这是普通段落，没有着重号。</p>
        <p><strong>这是粗体段落</strong>，也没有着重号。</p>
        <p style="color: red;">这是红色段落。</p>
        
        <h2>二、表格测试</h2>
        <table style="width: 100%; border-collapse: collapse; border-spacing: 0;">
          <tbody><tr>
            <th style="background-color: rgb(240, 240, 240); border: 1px solid black; padding: 6px 8px; vertical-align: top;">标题1</th>
            <th style="background-color: rgb(240, 240, 240); border: 1px solid black; padding: 6px 8px; vertical-align: top;">标题2</th>
          </tr>
          <tr>
            <td style="border: 1px solid black; padding: 6px 8px; vertical-align: top;">内容1</td>
            <td style="border: 1px solid black; padding: 6px 8px; vertical-align: top;">内容2</td>
          </tr>
        </tbody></table>
      
    
  </body></html>
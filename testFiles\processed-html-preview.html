<html><head></head><body>
        <h1>标准着重号测试</h1>
        <p>这里有<span style="font-weight: bold; background-color: rgb(255, 255, 153); text-decoration: underline; border: 1px dotted rgb(255, 0, 0);" data-emphasis-mark="dot" class="emphasis-mark">着重号文字1</span>。</p>
        <p>还有<span data-emphasis-mark="dot" style="font-weight: bold; background-color: rgb(255, 255, 153); text-decoration: underline; border: 1px dotted rgb(255, 0, 0);" class="emphasis-mark">着重号文字2</span>。</p>
        <p>以及<span data-emphasis-mark="dot" style="font-weight: bold; background-color: rgb(255, 255, 153); text-decoration: underline; border: 1px dotted rgb(255, 0, 0);" class="emphasis-mark">仅有属性的着重号</span>。</p>
        <p>普通文字没有着重号。</p>
        <p>预期效果：使用html-to-docx的粗体+背景色+下划线来表示着重号</p>
      
    
  </body></html>
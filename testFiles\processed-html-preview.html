<html><head></head><body>
          <h1>简单表格测试</h1>
          <p>这是一个简单的表格，测试边框效果：</p>
          
          <table style="width: 100%; border-collapse: collapse; border: 1px solid black;">
            <tbody><tr>
              <th style="border: 1px solid black; padding: 4px;">标题1</th>
              <th style="border: 1px solid black; padding: 4px;">标题2</th>
              <th style="border: 1px solid black; padding: 4px;">标题3</th>
            </tr>
            <tr>
              <td style="border: 1px solid black; padding: 4px;">内容1</td>
              <td style="border: 1px solid black; padding: 4px;">内容2</td>
              <td style="border: 1px solid black; padding: 4px;">内容3</td>
            </tr>
            <tr>
              <td style="border: 1px solid black; padding: 4px;">数据A</td>
              <td style="border: 1px solid black; padding: 4px;">数据B</td>
              <td style="border: 1px solid black; padding: 4px;">数据C</td>
            </tr>
          </tbody></table>
          
          <p>预期效果：简单的单线边框，不复杂。</p>
        
      
    </body></html>
<html><head></head><body>
        <h1>组合功能测试</h1>
        <p>这是一个包含<span style="font-weight: bold; background-color: rgb(255, 255, 153); text-decoration: underline; border: 1px dotted rgb(255, 0, 0);" data-emphasis-mark="dot" class="emphasis-mark">着重号</span>的段落。</p>
        
        <table style="border: 2px solid rgb(51, 51, 51); width: 100%; border-collapse: collapse; margin-top: 10px; margin-bottom: 10px;"><tr><th style="border: 1px solid rgb(102, 102, 102); padding: 8px; vertical-align: top; background-color: rgb(240, 240, 240); font-weight: bold; text-align: center;">项目</th><th style="border: 1px solid rgb(102, 102, 102); padding: 8px; vertical-align: top; background-color: rgb(240, 240, 240); font-weight: bold; text-align: center;">描述</th></tr><tr><td style="border: 1px solid rgb(102, 102, 102); padding: 8px; vertical-align: top;">着重号</td><td style="border: 1px solid rgb(102, 102, 102); padding: 8px; vertical-align: top;">使用<span data-emphasis-mark="dot" style="font-weight: bold; background-color: rgb(255, 255, 153); text-decoration: underline; border: 1px dotted rgb(255, 0, 0);" class="emphasis-mark">粗体+背景色+下划线</span>表示</td></tr><tr><td style="border: 1px solid rgb(102, 102, 102); padding: 8px; vertical-align: top;">表格</td><td style="border: 1px solid rgb(102, 102, 102); padding: 8px; vertical-align: top;">使用改进的边框和样式</td></tr></table>
        
        <p>测试完成！</p>
      
    
  </body></html>
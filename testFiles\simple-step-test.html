
    <html>
      <head>
        <meta charset="UTF-8">
        <title>简单着重号测试</title>
      </head>
      <body>
        <h1>简单着重号测试</h1>
        
        <h2>一、基础测试</h2>
        <p>这是普通段落，没有着重号。</p>
        <p>这是包含<strong>[EMPHASIS]着重号文字[/EMPHASIS]</strong>的段落。</p>
        <p>这是另一个包含<strong>[EMPHASIS]更多着重号[/EMPHASIS]</strong>的段落。</p>
        
        <h2>二、表格测试</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th style="background-color: #f0f0f0; padding: 8px;">题型</th>
            <th style="background-color: #f0f0f0; padding: 8px;">内容</th>
          </tr>
          <tr>
            <td style="padding: 8px;">选择题</td>
            <td style="padding: 8px;">包含<strong>[EMPHASIS]表格着重号[/EMPHASIS]</strong>的内容</td>
          </tr>
          <tr>
            <td style="padding: 8px;">填空题</td>
            <td style="padding: 8px;">普通表格内容</td>
          </tr>
        </table>
        
        <h2>三、列表测试</h2>
        <ul>
          <li>列表项1：<strong>[EMPHASIS]列表着重号[/EMPHASIS]</strong></li>
          <li>列表项2：普通列表内容</li>
        </ul>
        
        <h2>四、格式测试</h2>
        <p style="text-align: center; color: blue;">
          居中蓝色段落，包含<strong>[EMPHASIS]居中着重号[/EMPHASIS]</strong>。
        </p>
        <p style="text-align: right; font-size: 18px;">
          右对齐大字段落，包含<strong>[EMPHASIS]右对齐着重号[/EMPHASIS]</strong>。
        </p>
      </body>
    </html>
  
/**
 * 最终修复测试
 * 验证着重号Unicode字符和表格单线边框修复
 */
import * as path from 'path';
import { WordExportService } from '../service/word-export.service';

async function testFinalFix() {
  console.log('\n=== 最终修复测试：着重号Unicode + 表格单线边框 ===');
  
  const wordExportService = new WordExportService();
  
  const testHtml = `
    <html>
      <body>
        <h1>最终修复测试</h1>
        
        <h2>一、着重号测试（使用Unicode组合字符）</h2>
        <p><strong>1. 下面对有新疆房的语言的理解，错误的一项是（　　）</strong></p>
        <p>A. A. 边疆（<span data-emphasis-mark="dot" style="text-emphasis: filled currentColor;">重返国界的领土</span>）</p>
        <p>B. B. 绚丽多彩（<span data-emphasis-mark="dot">各种各样的色彩加以美丽</span>）</p>
        <p>C. C. 坪坝（<span data-emphasis-mark="dot">高高低低的坝地</span>）</p>
        <p>D. D. 高高低低工厂对有新疆房的可了的</p>
        
        <h2>二、表格测试（单线边框）</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
          <tr>
            <th style="border: 1px solid black; padding: 8px;">题型</th>
            <th style="border: 1px solid black; padding: 8px;">内容</th>
            <th style="border: 1px solid black; padding: 8px;">着重号测试</th>
          </tr>
          <tr>
            <td style="border: 1px solid black; padding: 8px;">选择题</td>
            <td style="border: 1px solid black; padding: 8px;">普通文字内容</td>
            <td style="border: 1px solid black; padding: 8px;"><span data-emphasis-mark="dot">着重号文字</span></td>
          </tr>
          <tr>
            <td style="border: 1px solid black; padding: 8px;">填空题</td>
            <td style="border: 1px solid black; padding: 8px;">更多测试内容</td>
            <td style="border: 1px solid black; padding: 8px;">无着重号</td>
          </tr>
          <tr>
            <td style="border: 1px solid black; padding: 8px;">判断题</td>
            <td style="border: 1px solid black; padding: 8px;"><span data-emphasis-mark="dot">表格内着重号</span></td>
            <td style="border: 1px solid black; padding: 8px;">混合内容</td>
          </tr>
        </table>
        
        <h2>三、复杂表格测试</h2>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr>
              <th style="background-color: #f0f0f0;">序号</th>
              <th style="background-color: #f0f0f0;">题目</th>
              <th style="background-color: #f0f0f0;">选项</th>
              <th style="background-color: #f0f0f0;">答案</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1</td>
              <td>这是一道<span data-emphasis-mark="dot">包含着重号</span>的题目</td>
              <td>A. 选项一<br/>B. 选项二</td>
              <td>A</td>
            </tr>
            <tr>
              <td>2</td>
              <td>普通题目内容</td>
              <td>A. <span data-emphasis-mark="dot">着重选项</span><br/>B. 普通选项</td>
              <td>B</td>
            </tr>
          </tbody>
        </table>
        
        <h2>四、段落格式保持测试</h2>
        <p style="text-align: center; font-size: 16px; color: blue;">
          居中的蓝色段落，包含<span data-emphasis-mark="dot">着重号文字</span>，测试格式保持。
        </p>
        <p style="text-align: right; font-weight: bold;">
          右对齐的粗体段落，包含<span data-emphasis-mark="dot">更多着重号</span>。
        </p>
        
        <h2>五、列表格式测试</h2>
        <ul>
          <li>列表项1：<span data-emphasis-mark="dot">着重号内容</span></li>
          <li>列表项2：普通内容</li>
          <li>列表项3：混合<span data-emphasis-mark="dot">着重号</span>和普通文字</li>
        </ul>
        
        <ol>
          <li>有序列表1：<span data-emphasis-mark="dot">数字着重号</span></li>
          <li>有序列表2：正常内容</li>
        </ol>
      </body>
    </html>
  `;
  
  try {
    const outputPath = path.join(process.cwd(), 'testFiles', 'final-fix-test.docx');
    
    // 使用标准方案，测试修复效果
    await wordExportService.exportHtmlToWordFile(testHtml, outputPath, {
      title: '最终修复测试',
      author: '测试系统',
      postprocessOptions: {
        processEmphasisMarks: false, // 使用预处理中的Unicode方案
      }
    });
    
    console.log('✅ 最终修复测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('\n🎯 修复内容：');
    console.log('1. 着重号：使用Unicode组合字符 (U+0323) 实现真正的着重号点');
    console.log('2. 表格：修复双线边框问题，确保单线边框显示');
    console.log('3. 格式：保持所有段落、列表、颜色等格式');
    console.log('\n📋 预期效果：');
    console.log('- 着重号：文字下方显示小点（真正的着重号效果）');
    console.log('- 表格：单线边框，无双线问题');
    console.log('- 格式：所有样式完整保持');
    return true;
  } catch (error) {
    console.log('❌ 最终修复测试失败:', error.message);
    return false;
  }
}

async function testComplexDocumentFinalFix() {
  console.log('\n=== 复杂文档最终修复测试 ===');
  
  const wordExportService = new WordExportService();
  
  try {
    // 读取原始的复杂HTML文件
    const fs = require('fs');
    const htmlFilePath = path.join(process.cwd(), 'testFiles', 'input-html-sample.html');
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf-8');
    console.log(`已读取HTML文件: ${htmlFilePath}`);

    const outputPath = path.join(process.cwd(), 'testFiles', 'complex-final-fix.docx');
    
    // 使用修复后的方案
    await wordExportService.exportHtmlToWordFile(htmlContent, outputPath, {
      title: '复杂文档最终修复',
      author: '测试系统',
      postprocessOptions: {
        processEmphasisMarks: false, // 使用Unicode着重号方案
      }
    });
    
    console.log('✅ 复杂文档最终修复测试通过');
    console.log(`Word文档已保存: ${outputPath}`);
    console.log('预期效果：');
    console.log('- 所有着重号显示为文字下方的小点');
    console.log('- 所有表格显示为单线边框');
    console.log('- 所有其他格式完整保持');
    return true;
  } catch (error) {
    console.log('❌ 复杂文档最终修复测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('开始最终修复测试...');
  
  const tests = [
    testFinalFix,
    testComplexDocumentFinalFix,
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    const result = await test();
    if (result) {
      passedTests++;
    }
  }
  
  console.log('\n=== 最终修复测试总结 ===');
  console.log(`总测试数: ${tests.length}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${tests.length - passedTests}`);
  
  if (passedTests === tests.length) {
    console.log('🎉 最终修复测试通过！');
    console.log('\n🔧 修复方案总结：');
    console.log('1. 着重号：使用Unicode组合字符 (\\u0323) 实现真正的下点效果');
    console.log('2. 表格：优化border-collapse和边框设置，消除双线问题');
    console.log('3. 兼容性：保持html-to-docx的所有功能，无破坏性修改');
    console.log('\n这是真正解决问题的最终方案！');
  } else {
    console.log('⚠️  部分最终修复测试失败，请检查相关功能。');
  }
  
  console.log('\n📁 生成的测试文件：');
  console.log('- final-fix-test.docx - 最终修复完整测试');
  console.log('- complex-final-fix.docx - 复杂文档最终修复');
}

// 仅在直接运行此文件时执行测试
if (require.main === module) {
  main().catch(console.error);
}

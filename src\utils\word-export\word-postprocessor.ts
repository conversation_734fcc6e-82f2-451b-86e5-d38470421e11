/**
 * Word文档后处理工具
 * 处理html-to-docx转换后需要进一步优化的内容
 */
import { Document, Packer, Paragraph, TextRun, UnderlineType } from 'docx';
import { JSDOM } from 'jsdom';

export interface PostprocessOptions {
  /** 是否处理着重号 */
  processEmphasisMarks?: boolean;
  /** 是否优化字体设置 */
  optimizeFonts?: boolean;
  /** 是否调整段落间距 */
  adjustParagraphSpacing?: boolean;
  /** 原始HTML内容，用于提取着重号信息 */
  originalHtmlContent?: string;
}

/**
 * Word文档后处理器
 */
export class WordPostprocessor {
  private docxBuffer: Buffer;

  constructor(docxBuffer: Buffer) {
    this.docxBuffer = docxBuffer;
  }

  /**
   * 执行后处理
   */
  async postprocess(options: PostprocessOptions = {}): Promise<Buffer> {
    const {
      processEmphasisMarks = true,
      optimizeFonts = true,
      adjustParagraphSpacing = true,
      originalHtmlContent,
    } = options;

    console.log('开始Word文档后处理...');

    if (processEmphasisMarks && originalHtmlContent) {
      // 使用docx库处理着重号
      console.log('使用docx库处理着重号...');
      return await this.processEmphasisWithDocx(originalHtmlContent);
    }

    if (optimizeFonts) {
      // 字体优化：输出提示信息
      console.log('字体优化：html-to-docx已处理基本字体样式');
    }

    if (adjustParagraphSpacing) {
      // 段落间距调整：输出提示信息
      console.log('段落间距调整：html-to-docx已处理基本段落格式');
    }

    console.log('Word文档后处理完成');
    return this.docxBuffer;
  }

  /**
   * 使用docx库处理着重号
   * 简化版本：直接从HTML提取内容并生成新的Word文档
   */
  private async processEmphasisWithDocx(originalHtmlContent: string): Promise<Buffer> {
    try {
      console.log('使用docx库重新生成带着重号的Word文档...');

      // 解析原始HTML以获取着重号信息
      const emphasisInfo = this.extractEmphasisFromHtml(originalHtmlContent);
      console.log(`从HTML中提取了 ${emphasisInfo.length} 个着重号`);

      // 使用docx库重新生成文档
      const newDocxBuffer = await this.createDocxWithEmphasis(emphasisInfo);

      console.log('使用docx库处理着重号完成');
      return newDocxBuffer;

    } catch (error) {
      console.error('使用docx库处理着重号失败:', error);
      console.log('回退到原始Word文档');
      return this.docxBuffer;
    }
  }



  /**
   * 从HTML中提取着重号信息
   */
  private extractEmphasisFromHtml(htmlContent: string): Array<{text: string, isEmphasis: boolean}> {
    const emphasisInfo: Array<{text: string, isEmphasis: boolean}> = [];

    try {
      // 使用JSDOM解析HTML
      const dom = new JSDOM(htmlContent);
      const document = dom.window.document;

      // 递归处理所有文本节点
      this.processTextNodes(document.body, emphasisInfo);

    } catch (error) {
      console.error('解析HTML失败，使用简单文本提取:', error);
      // 回退到简单的文本提取
      const textContent = htmlContent.replace(/<[^>]*>/g, ' ').trim();
      if (textContent) {
        emphasisInfo.push({
          text: textContent,
          isEmphasis: false
        });
      }
    }

    return emphasisInfo;
  }

  /**
   * 递归处理文本节点
   */
  private processTextNodes(node: Node, emphasisInfo: Array<{text: string, isEmphasis: boolean}>): void {
    if (node.nodeType === 3) { // 文本节点
      const text = node.textContent?.trim();
      if (text) {
        // 检查父元素是否有着重号标记
        const parent = node.parentElement;
        const isEmphasis = parent && (
          parent.hasAttribute('data-emphasis-mark') ||
          parent.classList.contains('emphasis-mark') ||
          parent.style.textEmphasis
        );

        emphasisInfo.push({
          text,
          isEmphasis: !!isEmphasis
        });
      }
    } else if (node.nodeType === 1) { // 元素节点
      // 递归处理子节点
      for (let i = 0; i < node.childNodes.length; i++) {
        this.processTextNodes(node.childNodes[i], emphasisInfo);
      }
    }
  }

  /**
   * 使用docx库创建带有着重号的文档
   */
  private async createDocxWithEmphasis(
    emphasisInfo: Array<{text: string, isEmphasis: boolean}>
  ): Promise<Buffer> {
    const paragraphs: Paragraph[] = [];

    // 简化处理：根据着重号信息创建段落
    if (emphasisInfo.length === 0) {
      // 如果没有着重号，创建一个默认段落
      paragraphs.push(new Paragraph({
        children: [new TextRun({ text: '没有找到内容' })]
      }));
    } else {
      // 按着重号信息分组创建段落
      let currentParagraphRuns: TextRun[] = [];

      emphasisInfo.forEach(info => {
        if (info.isEmphasis) {
          // 添加着重号文本
          currentParagraphRuns.push(new TextRun({
            text: info.text,
            bold: true,
            underline: {
              type: UnderlineType.DOTTED,
              color: '000000',
            },
            highlight: 'yellow',
          }));
        } else {
          // 添加普通文本
          currentParagraphRuns.push(new TextRun({ text: info.text }));
        }
      });

      // 创建段落
      if (currentParagraphRuns.length > 0) {
        paragraphs.push(new Paragraph({ children: currentParagraphRuns }));
      }
    }

    // 创建Word文档
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: paragraphs,
        },
      ],
    });

    return await Packer.toBuffer(doc);
  }

  /**
   * 使用docx库创建增强的Word文档
   * 这是一个备用方案，当html-to-docx无法满足需求时使用
   */
  async createEnhancedDocument(
    htmlContent: string,
    options: any = {}
  ): Promise<Buffer> {
    console.log('使用docx库创建增强Word文档...');

    // 创建新的Word文档
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: [
            new Paragraph({
              children: [
                new TextRun({
                  text: '这是使用docx库创建的文档',
                  bold: true,
                }),
              ],
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: '当html-to-docx无法满足特殊需求时，可以使用此方法',
                }),
              ],
            }),
          ],
        },
      ],
    });

    // 生成文档Buffer
    const buffer = await Packer.toBuffer(doc);
    return buffer;
  }
}

/**
 * 后处理Word文档
 * @param docxBuffer 原始Word文档Buffer
 * @param options 后处理选项
 * @returns 后处理后的Word文档Buffer
 */
export async function postprocessWord(
  docxBuffer: Buffer,
  options: PostprocessOptions = {}
): Promise<Buffer> {
  const postprocessor = new WordPostprocessor(docxBuffer);
  return await postprocessor.postprocess(options);
}

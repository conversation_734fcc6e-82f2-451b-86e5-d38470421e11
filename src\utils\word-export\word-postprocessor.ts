/**
 * Word文档后处理工具
 * 处理html-to-docx转换后需要进一步优化的内容
 */
import { Document, Packer, Paragraph, TextRun } from 'docx';

export interface PostprocessOptions {
  /** 是否处理着重号 */
  processEmphasisMarks?: boolean;
  /** 是否优化字体设置 */
  optimizeFonts?: boolean;
  /** 是否调整段落间距 */
  adjustParagraphSpacing?: boolean;
}

/**
 * Word文档后处理器
 */
export class WordPostprocessor {
  private docxBuffer: Buffer;

  constructor(docxBuffer: Buffer) {
    this.docxBuffer = docxBuffer;
  }

  /**
   * 执行后处理
   */
  async postprocess(options: PostprocessOptions = {}): Promise<Buffer> {
    const {
      processEmphasisMarks = true,
      optimizeFonts = true,
      adjustParagraphSpacing = true,
    } = options;

    console.log('开始Word文档后处理...');

    // 目前html-to-docx已经能处理大部分转换需求
    // 这里预留后处理接口，用于将来可能需要的高级处理
    
    if (processEmphasisMarks) {
      // 预留：处理着重号的进一步优化
      console.log('着重号后处理（预留）');
    }

    if (optimizeFonts) {
      // 预留：字体优化
      console.log('字体优化（预留）');
    }

    if (adjustParagraphSpacing) {
      // 预留：段落间距调整
      console.log('段落间距调整（预留）');
    }

    console.log('Word文档后处理完成');
    return this.docxBuffer;
  }

  /**
   * 使用docx库创建增强的Word文档
   * 这是一个备用方案，当html-to-docx无法满足需求时使用
   */
  async createEnhancedDocument(
    htmlContent: string,
    options: any = {}
  ): Promise<Buffer> {
    console.log('使用docx库创建增强Word文档...');

    // 创建新的Word文档
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: [
            new Paragraph({
              children: [
                new TextRun({
                  text: '这是使用docx库创建的文档',
                  bold: true,
                }),
              ],
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: '当html-to-docx无法满足特殊需求时，可以使用此方法',
                }),
              ],
            }),
          ],
        },
      ],
    });

    // 生成文档Buffer
    const buffer = await Packer.toBuffer(doc);
    return buffer;
  }
}

/**
 * 后处理Word文档
 * @param docxBuffer 原始Word文档Buffer
 * @param options 后处理选项
 * @returns 后处理后的Word文档Buffer
 */
export async function postprocessWord(
  docxBuffer: Buffer,
  options: PostprocessOptions = {}
): Promise<Buffer> {
  const postprocessor = new WordPostprocessor(docxBuffer);
  return await postprocessor.postprocess(options);
}
